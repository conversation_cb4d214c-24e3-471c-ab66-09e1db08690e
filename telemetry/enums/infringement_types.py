from enum import Enum

class InfringementTypes(Enum):
	BLOCKING_BY_SLOW_DRIVING = 0
	BLOCKING_BY_WRONG_WAY_DRIVING = 1
	REVERSING_OFF_THE_START_LINE = 2
	BIG_COLLISION = 3
	SMALL_COLLISION = 4
	COLLISION_FAILED_TO_HAND_BACK_POSITION_SINGLE = 5
	COLLISION_FAILED_TO_HAND_BACK_POSITION_MULTIPLE = 6
	CORNER_CUTTING_GAINED_TIME = 7
	CORNER_CUTTING_OVERTAKE_SINGLE = 8
	CORNER_CUTTING_OVERTAKE_MULTIPLE = 9
	CROSSED_PIT_EXIT_LANE = 10
	IGNORING_BLUE_FLAGS = 11
	IGNORING_YELLOW_FLAGS = 12
	IGNORING_DRIVE_THROUGH = 13
	TOO_MANY_DRIVE_THROUGHS = 14
	DRIVE_THROUGH_REMINDER_SERVE_WITHIN_N_LAPS = 15
	DRIVE_THROUGH_REMINDER_SERVE_THIS_LAP = 16
	PIT_LANE_SPEEDING = 17
	PARKED_FOR_TOO_LONG = 18
	IGNORING_TYRE_REGULATIONS = 19
	TOO_MANY_PENALTIES = 20
	MULTIPLE_WARNINGS = 21
	APPROACHING_DISQUALIFICATION = 22
	TYRE_REGULATIONS_SELECT_SINGLE = 23
	TYRE_REGULATIONS_SELECT_MULTIPLE = 24
	LAP_INVALIDATED_CORNER_CUTTING = 25
	LAP_INVALIDATED_RUNNING_WIDE = 26
	CORNER_CUTTING_RAN_WIDE_GAINED_TIME_MINOR = 27
	CORNER_CUTTING_RAN_WIDE_GAINED_TIME_SIGNIFICANT = 28
	CORNER_CUTTING_RAN_WIDE_GAINED_TIME_EXTREME = 29
	LAP_INVALIDATED_WALL_RIDING = 30
	LAP_INVALIDATED_FLASHBACK_USED = 31
	LAP_INVALIDATED_RESET_TO_TRACK = 32
	BLOCKING_THE_PITLANE = 33
	JUMP_START = 34
	SAFETY_CAR_TO_CAR_COLLISION = 35
	SAFETY_CAR_ILLEGAL_OVERTAKE = 36
	SAFETY_CAR_EXCEEDING_ALLOWED_PACE = 37
	VIRTUAL_SAFETY_CAR_EXCEEDING_ALLOWED_PACE = 38
	FORMATION_LAP_BELOW_ALLOWED_SPEED = 39
	FORMATION_LAP_PARKING = 40
	RETIRED_MECHANICAL_FAILURE = 41
	RETIRED_TERMINALLY_DAMAGED = 42
	SAFETY_CAR_FALLING_TOO_FAR_BACK = 43
	BLACK_FLAG_TIMER = 44
	UNSERVED_STOP_GO_PENALTY = 45
	UNSERVED_DRIVE_THROUGH_PENALTY = 46
	ENGINE_COMPONENT_CHANGE = 47
	GEARBOX_CHANGE = 48
	PARC_FERME_CHANGE = 49
	LEAGUE_GRID_PENALTY = 50
	RETRY_PENALTY = 51
	ILLEGAL_TIME_GAIN = 52
	MANDATORY_PITSTOP = 53
	ATTRIBUTE_ASSIGNED = 54