from dataclasses import dataclass
from typing import Optional
from datetime import datetime

@dataclass
class Race:
    """
    Modelo para representar una carrera/sesión completa de F1 24
    """
    id: Optional[int] = None
    session_uid: int = 0
    track_id: int = -1
    track_name: str = ""
    session_type: str = ""
    session_name: str = ""
    timestamp: str = ""
    game_mode: int = 0
    total_laps: int = 0
    session_duration: int = 0
    weather: int = 0
    track_temperature: int = 0
    air_temperature: int = 0
    mfd_data: str = ""  # JSON serializado del MultiFunctionDisplay
    is_completed: bool = False
    
    def __post_init__(self):
        """Genera un nombre de sesión automático si no se proporciona"""
        if not self.session_name and self.track_name and self.session_type:
            timestamp_obj = datetime.fromisoformat(self.timestamp) if self.timestamp else datetime.now()
            date_str = timestamp_obj.strftime("%Y-%m-%d %H:%M")
            self.session_name = f"{self.track_name} - {self.session_type} ({date_str})"
    
    def to_dict(self):
        """Convierte el objeto Race a diccionario para serialización"""
        return {
            'id': self.id,
            'session_uid': self.session_uid,
            'track_id': self.track_id,
            'track_name': self.track_name,
            'session_type': self.session_type,
            'session_name': self.session_name,
            'timestamp': self.timestamp,
            'game_mode': self.game_mode,
            'total_laps': self.total_laps,
            'session_duration': self.session_duration,
            'weather': self.weather,
            'track_temperature': self.track_temperature,
            'air_temperature': self.air_temperature,
            'is_completed': self.is_completed
        }
    
    @classmethod
    def from_dict(cls, data: dict):
        """Crea un objeto Race desde un diccionario"""
        return cls(
            id=data.get('id'),
            session_uid=data.get('session_uid', 0),
            track_id=data.get('track_id', -1),
            track_name=data.get('track_name', ''),
            session_type=data.get('session_type', ''),
            session_name=data.get('session_name', ''),
            timestamp=data.get('timestamp', ''),
            game_mode=data.get('game_mode', 0),
            total_laps=data.get('total_laps', 0),
            session_duration=data.get('session_duration', 0),
            weather=data.get('weather', 0),
            track_temperature=data.get('track_temperature', 0),
            air_temperature=data.get('air_temperature', 0),
            mfd_data=data.get('mfd_data', ''),
            is_completed=data.get('is_completed', False)
        )
