# F1 24 Dashboard - Multiple Races Feature Setup

## Overview

This update adds support for storing and viewing multiple races instead of just the latest one. You can now:

- **Store multiple races**: Each F1 24 session is automatically saved as a separate race
- **Historical viewing**: Browse and view data from previous races
- **Advanced filtering**: Filter races by track, session type, date range, and search text
- **Live/Historical modes**: Switch between real-time telemetry and historical race data

## New Features

### Backend Changes
- **Multiple race storage**: New database table and models for storing multiple races
- **Session detection**: Automatic detection of new sessions and race completion
- **Race metadata**: Track names, session types, timestamps, and completion status
- **API endpoints**: New REST API endpoints for race management

### Frontend Changes
- **Race selector**: New component for browsing and selecting races
- **Mode switching**: Toggle between Live and Historical modes
- **Advanced filters**: Filter races by multiple criteria
- **Race information**: Display selected race details and status

## Installation and Setup

### 1. Backup Your Current Data (Recommended)
```bash
# Create a backup of your current database
cp database/mfd.db database/mfd_backup_$(date +%Y%m%d_%H%M%S).db
```

### 2. Run the Migration Script
```bash
# Migrate existing MFD data to the new structure
python migrate_existing_data.py
```

### 3. Install/Update Dependencies
```bash
# Backend dependencies (if any new ones were added)
pip install -r requirements.txt

# Frontend dependencies
cd frontend
npm install
cd ..
```

### 4. Build Frontend (if needed)
```bash
cd frontend
npm run build
cd ..
```

## Usage

### Starting the System
1. **Start the telemetry server**:
   ```bash
   python telemetry/main.py
   ```

2. **Start the web server**:
   ```bash
   python frontend/server.py
   ```

3. **Open your browser** and navigate to `http://localhost:5000`

### Using the New Features

#### Live Mode (Default)
- Shows real-time telemetry data from F1 24
- Automatically saves each session as a new race
- Works exactly like the original system

#### Historical Mode
1. Click "Show Race Selector" button (top-right)
2. Switch to "Historical Mode"
3. Use filters to find specific races:
   - **Search**: Search by race name
   - **Track**: Filter by circuit
   - **Session Type**: Filter by Practice, Qualifying, Race, etc.
   - **Date Range**: Filter by date
4. Click on a race to view its data

#### Race Management
- Races are automatically saved when sessions end
- Each race includes complete telemetry data
- Completed races are marked with a green "Completed" badge
- In-progress races show a yellow "In Progress" badge

## API Endpoints

### New Endpoints
- `GET /api/races` - List all races with optional filters
- `GET /api/races/{id}` - Get specific race data
- `GET /api/tracks` - Get list of available tracks
- `GET /api/session-types` - Get list of session types

### Existing Endpoints (Unchanged)
- `GET /api/mfd` - Current live telemetry data

## Database Schema

### New Table: `races`
```sql
CREATE TABLE races (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_uid INTEGER NOT NULL,
    track_id INTEGER,
    track_name TEXT,
    session_type TEXT,
    session_name TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    game_mode INTEGER,
    total_laps INTEGER,
    session_duration INTEGER,
    weather INTEGER,
    track_temperature INTEGER,
    air_temperature INTEGER,
    mfd_data TEXT,
    is_completed BOOLEAN DEFAULT FALSE,
    UNIQUE(session_uid)
);
```

## Troubleshooting

### Common Issues

1. **"No races found"**
   - Make sure you've run the migration script
   - Start F1 24 and complete at least one session
   - Check that the telemetry server is running

2. **Race selector not showing**
   - Click the "Show Race Selector" button in the top-right
   - Make sure JavaScript is enabled in your browser

3. **Historical data not loading**
   - Check browser console for errors
   - Verify the web server is running on port 5000
   - Ensure the race has MFD data stored

4. **Migration issues**
   - Check that you have write permissions to the database directory
   - Ensure no other processes are using the database file

### Database Utilities

Use the database utilities script for maintenance:
```bash
python database_utils.py
```

This provides options to:
- List all races
- Show race details
- View database statistics
- Clean test data
- Create database backups

## Technical Details

### Session Detection
- New sessions are detected by changes in `session_uid`
- Session start/end events trigger race creation/completion
- Each race is uniquely identified by its `session_uid`

### Data Storage
- Complete MFD data is stored as JSON for each race
- Metadata is extracted for efficient filtering
- Original MFD table is preserved for compatibility

### Compatibility
- Existing functionality remains unchanged
- Original API endpoints still work
- Live mode works exactly as before

## Development Notes

### File Structure
```
database/
├── models/
│   ├── race.py              # New Race model
│   └── multi_functional_display.py  # Updated with session metadata
├── repositories/
│   ├── race_repository.py   # New race management functions
│   └── mfd_repository.py    # Original MFD functions (unchanged)

frontend/src/
├── components/
│   ├── RaceSelector.js      # New race selection component
│   └── ...                  # Existing components (unchanged)
├── services/
│   └── api.js              # Updated with new API functions

telemetry/
└── main.py                 # Updated with session detection

migrate_existing_data.py    # Migration script
database_utils.py          # Database utilities
```

### Key Components
- **Race Model**: Represents a complete F1 session with metadata
- **Race Repository**: Database operations for multiple races
- **RaceSelector Component**: UI for browsing and filtering races
- **Session Detection**: Automatic detection of new F1 sessions

## Testing

Run the test script to validate your installation:
```bash
python test_multiple_races.py
```

This will test:
- Database connectivity
- API endpoints
- Race creation and retrieval
- Frontend component loading

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review the console output for error messages
3. Use the database utilities to inspect your data
4. Run the test script to identify problems
5. Create a backup before making changes

## Future Enhancements

Potential future features:
- Race comparison tools
- Export race data
- Race statistics and analytics
- Shared race data between users
- Race replay functionality
