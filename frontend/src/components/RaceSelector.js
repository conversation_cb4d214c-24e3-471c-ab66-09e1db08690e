import React, { useState, useEffect } from 'react';
import { fetchRaces, fetchTracks, fetchSessionTypes } from '../services/api';

const RaceSelector = ({ onRaceSelect, onModeChange, currentMode, selectedRace }) => {
    const [races, setRaces] = useState([]);
    const [filteredRaces, setFilteredRaces] = useState([]);
    const [tracks, setTracks] = useState([]);
    const [sessionTypes, setSessionTypes] = useState([]);
    const [loading, setLoading] = useState(false);
    
    // Filter states
    const [filters, setFilters] = useState({
        trackName: '',
        sessionType: '',
        dateFrom: '',
        dateTo: '',
        searchText: ''
    });
    
    const [showFilters, setShowFilters] = useState(false);

    useEffect(() => {
        loadInitialData();
    }, []);

    useEffect(() => {
        applyFilters();
    }, [races, filters]);

    const loadInitialData = async () => {
        setLoading(true);
        try {
            const [racesData, tracksData, sessionTypesData] = await Promise.all([
                fetchRaces(),
                fetchTracks(),
                fetchSessionTypes()
            ]);
            
            setRaces(racesData.races || []);
            setTracks(tracksData.tracks || []);
            setSessionTypes(sessionTypesData.session_types || []);
        } catch (error) {
            console.error('Error loading race data:', error);
        } finally {
            setLoading(false);
        }
    };

    const applyFilters = () => {
        let filtered = [...races];

        // Filter by track name
        if (filters.trackName) {
            filtered = filtered.filter(race => 
                race.track_name.toLowerCase().includes(filters.trackName.toLowerCase())
            );
        }

        // Filter by session type
        if (filters.sessionType) {
            filtered = filtered.filter(race => race.session_type === filters.sessionType);
        }

        // Filter by date range
        if (filters.dateFrom) {
            filtered = filtered.filter(race => 
                new Date(race.timestamp) >= new Date(filters.dateFrom)
            );
        }

        if (filters.dateTo) {
            filtered = filtered.filter(race => 
                new Date(race.timestamp) <= new Date(filters.dateTo + 'T23:59:59')
            );
        }

        // Filter by search text (searches in session name)
        if (filters.searchText) {
            filtered = filtered.filter(race =>
                race.session_name.toLowerCase().includes(filters.searchText.toLowerCase())
            );
        }

        setFilteredRaces(filtered);
    };

    const handleFilterChange = (filterName, value) => {
        setFilters(prev => ({
            ...prev,
            [filterName]: value
        }));
    };

    const clearFilters = () => {
        setFilters({
            trackName: '',
            sessionType: '',
            dateFrom: '',
            dateTo: '',
            searchText: ''
        });
    };

    const formatDate = (timestamp) => {
        return new Date(timestamp).toLocaleString();
    };

    const formatSessionType = (sessionType) => {
        return sessionType.replace(/_/g, ' ').toLowerCase()
            .replace(/\b\w/g, l => l.toUpperCase());
    };

    return (
        <div className="bg-mainLight rounded-lg p-4 mb-4">
            {/* Mode Toggle */}
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-mainWhite">Race Selection</h2>
                <div className="flex gap-2">
                    <button
                        onClick={() => onModeChange('live')}
                        className={`px-4 py-2 rounded ${
                            currentMode === 'live' 
                                ? 'bg-green-600 text-white' 
                                : 'bg-gray-600 text-gray-300'
                        }`}
                    >
                        Live Mode
                    </button>
                    <button
                        onClick={() => onModeChange('historical')}
                        className={`px-4 py-2 rounded ${
                            currentMode === 'historical' 
                                ? 'bg-blue-600 text-white' 
                                : 'bg-gray-600 text-gray-300'
                        }`}
                    >
                        Historical Mode
                    </button>
                </div>
            </div>

            {/* Historical Mode Content */}
            {currentMode === 'historical' && (
                <>
                    {/* Filter Toggle */}
                    <div className="mb-4">
                        <button
                            onClick={() => setShowFilters(!showFilters)}
                            className="bg-mainDark text-mainWhite px-4 py-2 rounded hover:bg-opacity-80"
                        >
                            {showFilters ? 'Hide Filters' : 'Show Filters'}
                        </button>
                    </div>

                    {/* Filters */}
                    {showFilters && (
                        <div className="bg-mainDark rounded p-4 mb-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {/* Search Text */}
                                <div>
                                    <label className="block text-sm font-medium text-mainWhite mb-1">
                                        Search
                                    </label>
                                    <input
                                        type="text"
                                        value={filters.searchText}
                                        onChange={(e) => handleFilterChange('searchText', e.target.value)}
                                        placeholder="Search race name..."
                                        className="w-full px-3 py-2 bg-mainLight text-mainWhite rounded border border-mainBorder"
                                    />
                                </div>

                                {/* Track Filter */}
                                <div>
                                    <label className="block text-sm font-medium text-mainWhite mb-1">
                                        Track
                                    </label>
                                    <select
                                        value={filters.trackName}
                                        onChange={(e) => handleFilterChange('trackName', e.target.value)}
                                        className="w-full px-3 py-2 bg-mainLight text-mainWhite rounded border border-mainBorder"
                                    >
                                        <option value="">All Tracks</option>
                                        {tracks.map(track => (
                                            <option key={track} value={track}>{track}</option>
                                        ))}
                                    </select>
                                </div>

                                {/* Session Type Filter */}
                                <div>
                                    <label className="block text-sm font-medium text-mainWhite mb-1">
                                        Session Type
                                    </label>
                                    <select
                                        value={filters.sessionType}
                                        onChange={(e) => handleFilterChange('sessionType', e.target.value)}
                                        className="w-full px-3 py-2 bg-mainLight text-mainWhite rounded border border-mainBorder"
                                    >
                                        <option value="">All Sessions</option>
                                        {sessionTypes.map(type => (
                                            <option key={type} value={type}>
                                                {formatSessionType(type)}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                {/* Date From */}
                                <div>
                                    <label className="block text-sm font-medium text-mainWhite mb-1">
                                        From Date
                                    </label>
                                    <input
                                        type="date"
                                        value={filters.dateFrom}
                                        onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                                        className="w-full px-3 py-2 bg-mainLight text-mainWhite rounded border border-mainBorder"
                                    />
                                </div>

                                {/* Date To */}
                                <div>
                                    <label className="block text-sm font-medium text-mainWhite mb-1">
                                        To Date
                                    </label>
                                    <input
                                        type="date"
                                        value={filters.dateTo}
                                        onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                                        className="w-full px-3 py-2 bg-mainLight text-mainWhite rounded border border-mainBorder"
                                    />
                                </div>

                                {/* Clear Filters */}
                                <div className="flex items-end">
                                    <button
                                        onClick={clearFilters}
                                        className="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                                    >
                                        Clear Filters
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Race List */}
                    <div className="max-h-96 overflow-y-auto">
                        {loading ? (
                            <div className="text-center text-mainWhite py-4">Loading races...</div>
                        ) : filteredRaces.length === 0 ? (
                            <div className="text-center text-mainWhite py-4">No races found</div>
                        ) : (
                            <div className="space-y-2">
                                {filteredRaces.map(race => (
                                    <div
                                        key={race.id}
                                        onClick={() => onRaceSelect(race)}
                                        className={`p-3 rounded cursor-pointer border transition-colors ${
                                            selectedRace?.id === race.id
                                                ? 'bg-blue-600 border-blue-400'
                                                : 'bg-mainDark border-mainBorder hover:bg-opacity-80'
                                        }`}
                                    >
                                        <div className="flex justify-between items-start">
                                            <div>
                                                <h3 className="font-semibold text-mainWhite">
                                                    {race.session_name}
                                                </h3>
                                                <p className="text-sm text-gray-300">
                                                    {race.track_name} - {formatSessionType(race.session_type)}
                                                </p>
                                                <p className="text-xs text-gray-400">
                                                    {formatDate(race.timestamp)}
                                                </p>
                                            </div>
                                            <div className="text-right">
                                                <span className={`px-2 py-1 rounded text-xs ${
                                                    race.is_completed 
                                                        ? 'bg-green-600 text-white' 
                                                        : 'bg-yellow-600 text-white'
                                                }`}>
                                                    {race.is_completed ? 'Completed' : 'In Progress'}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </>
            )}

            {/* Live Mode Content */}
            {currentMode === 'live' && (
                <div className="text-center text-mainWhite py-8">
                    <h3 className="text-lg font-semibold mb-2">Live Telemetry Mode</h3>
                    <p className="text-gray-300">
                        Displaying real-time data from the current F1 24 session
                    </p>
                </div>
            )}
        </div>
    );
};

export default RaceSelector;
