#!/usr/bin/env python3
"""
Script de inicio seguro para el servidor de telemetría F1 24 Dashboard
Incluye verificaciones previas y manejo de errores robusto
"""

import sys
import os
import time
import subprocess
from datetime import datetime

def log_message(message):
    """Log message with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_dependencies():
    """Verifica que todas las dependencias estén disponibles"""
    log_message("🔍 Checking dependencies...")
    
    try:
        sys.path.append('.')
        
        # Verificar imports críticos
        from database.models import MultiFunctionDisplay, Race
        from database.repositories import init_db, init_races_db, save_race
        from telemetry.enums import PacketIDs, EventCodes
        from telemetry.packets import unpack_packet_header
        
        log_message("✅ All dependencies available")
        return True
        
    except ImportError as e:
        log_message(f"❌ Missing dependency: {e}")
        return False
    except Exception as e:
        log_message(f"❌ Error checking dependencies: {e}")
        return False

def check_database():
    """Verifica que la base de datos esté accesible"""
    log_message("🔍 Checking database...")
    
    try:
        sys.path.append('.')
        from database.repositories import init_db, init_races_db, get_all_races
        
        # Inicializar bases de datos
        init_db()
        init_races_db()
        
        # Probar acceso
        races = get_all_races()
        log_message(f"✅ Database accessible ({len(races)} races found)")
        return True
        
    except Exception as e:
        log_message(f"❌ Database error: {e}")
        return False

def check_port():
    """Verifica que el puerto esté disponible"""
    log_message("🔍 Checking port availability...")
    
    try:
        import socket
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.bind(('127.0.0.1', 20777))
        sock.close()
        
        log_message("✅ Port 20777 is available")
        return True
        
    except OSError as e:
        log_message(f"❌ Port 20777 is not available: {e}")
        return False
    except Exception as e:
        log_message(f"❌ Error checking port: {e}")
        return False

def backup_database():
    """Crea una copia de seguridad de la base de datos"""
    log_message("💾 Creating database backup...")
    
    try:
        import shutil
        
        db_file = 'database/mfd.db'
        if os.path.exists(db_file):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"database/mfd_backup_{timestamp}.db"
            shutil.copy2(db_file, backup_file)
            log_message(f"✅ Database backed up to: {backup_file}")
        else:
            log_message("ℹ️  No existing database to backup")
        
        return True
        
    except Exception as e:
        log_message(f"❌ Backup failed: {e}")
        return False

def run_stability_test():
    """Ejecuta pruebas de estabilidad básicas"""
    log_message("🧪 Running stability tests...")
    
    try:
        # Ejecutar script de pruebas si existe
        if os.path.exists('test_system_stability.py'):
            result = subprocess.run([sys.executable, 'test_system_stability.py'], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                log_message("✅ Stability tests passed")
                return True
            else:
                log_message("❌ Stability tests failed")
                log_message(f"Error output: {result.stderr}")
                return False
        else:
            log_message("ℹ️  Stability test script not found, skipping")
            return True
            
    except subprocess.TimeoutExpired:
        log_message("⚠️  Stability tests timed out")
        return False
    except Exception as e:
        log_message(f"❌ Error running stability tests: {e}")
        return False

def start_telemetry_server():
    """Inicia el servidor de telemetría"""
    log_message("🚀 Starting telemetry server...")
    
    try:
        # Cambiar al directorio del script
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        # Ejecutar el servidor
        subprocess.run([sys.executable, 'telemetry/main.py'])
        
    except KeyboardInterrupt:
        log_message("🛑 Server stopped by user")
    except Exception as e:
        log_message(f"❌ Error starting server: {e}")
        return False
    
    return True

def main():
    """Función principal"""
    print("=" * 80)
    print("F1 24 Dashboard - Telemetry Server Startup")
    print("=" * 80)
    print()
    
    # Lista de verificaciones previas
    checks = [
        ("Dependencies", check_dependencies),
        ("Database", check_database),
        ("Port Availability", check_port),
        ("Database Backup", backup_database),
        ("Stability Tests", run_stability_test),
    ]
    
    # Ejecutar verificaciones
    all_passed = True
    for check_name, check_func in checks:
        log_message(f"Running {check_name} check...")
        try:
            if not check_func():
                all_passed = False
                log_message(f"❌ {check_name} check failed")
            else:
                log_message(f"✅ {check_name} check passed")
        except Exception as e:
            log_message(f"❌ {check_name} check failed with exception: {e}")
            all_passed = False
        
        print()  # Separador
    
    if not all_passed:
        log_message("⚠️  Some pre-flight checks failed. Continue anyway? (y/N)")
        response = input().strip().lower()
        if response != 'y':
            log_message("🛑 Startup cancelled")
            return False
    
    log_message("🎯 All checks passed! Starting server...")
    print()
    
    # Mostrar información importante
    log_message("📋 Server Information:")
    log_message("   - Port: 20777 (UDP)")
    log_message("   - Multiple races support: ENABLED")
    log_message("   - Auto-save: ENABLED")
    log_message("   - Error handling: ROBUST")
    print()
    
    log_message("🎮 F1 24 Setup:")
    log_message("   1. Start F1 24 game")
    log_message("   2. Go to Settings > Telemetry Settings")
    log_message("   3. Set UDP Telemetry to 'On'")
    log_message("   4. Set IP Address to '127.0.0.1'")
    log_message("   5. Set Port to '20777'")
    log_message("   6. Set UDP Format to '2024'")
    print()
    
    log_message("🌐 Web Interface:")
    log_message("   - Start web server: python frontend/server.py")
    log_message("   - Open browser: http://localhost:5000")
    print()
    
    log_message("Press Ctrl+C to stop the server")
    print("=" * 80)
    
    # Iniciar servidor
    return start_telemetry_server()

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("🛑 Startup interrupted by user")
        sys.exit(0)
    except Exception as e:
        log_message(f"❌ Fatal error during startup: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
