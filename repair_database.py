#!/usr/bin/env python3
"""
Script para verificar y reparar la base de datos del F1 24 Dashboard
"""

import sys
import sqlite3
import json
import os
from datetime import datetime

sys.path.append('.')

def log_message(message):
    """Log message with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_database_file():
    """Verifica que el archivo de base de datos exista"""
    db_file = 'database/mfd.db'
    
    if not os.path.exists(db_file):
        log_message("❌ Database file does not exist")
        return False
    
    if not os.access(db_file, os.R_OK | os.W_OK):
        log_message("❌ Database file is not readable/writable")
        return False
    
    log_message("✅ Database file exists and is accessible")
    return True

def check_database_structure():
    """Verifica la estructura de la base de datos"""
    log_message("🔍 Checking database structure...")
    
    try:
        conn = sqlite3.connect('database/mfd.db')
        c = conn.cursor()
        
        # Verificar tabla original
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='multi_function_displays'")
        mfd_table = c.fetchone()
        
        # Verificar nueva tabla
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='races'")
        races_table = c.fetchone()
        
        conn.close()
        
        if not mfd_table:
            log_message("❌ Original MFD table missing")
            return False
        
        if not races_table:
            log_message("❌ Races table missing")
            return False
        
        log_message("✅ Database structure is correct")
        return True
        
    except Exception as e:
        log_message(f"❌ Error checking database structure: {e}")
        return False

def repair_database_structure():
    """Repara la estructura de la base de datos"""
    log_message("🔧 Repairing database structure...")
    
    try:
        from database.repositories import init_db, init_races_db
        
        # Inicializar tablas
        init_db()
        init_races_db()
        
        log_message("✅ Database structure repaired")
        return True
        
    except Exception as e:
        log_message(f"❌ Error repairing database structure: {e}")
        return False

def check_data_integrity():
    """Verifica la integridad de los datos"""
    log_message("🔍 Checking data integrity...")
    
    try:
        conn = sqlite3.connect('database/mfd.db')
        c = conn.cursor()
        
        # Verificar datos MFD
        c.execute("SELECT COUNT(*) FROM multi_function_displays")
        mfd_count = c.fetchone()[0]
        
        # Verificar datos de carreras
        c.execute("SELECT COUNT(*) FROM races")
        races_count = c.fetchone()[0]
        
        # Verificar carreras con datos JSON válidos
        c.execute("SELECT COUNT(*) FROM races WHERE mfd_data IS NOT NULL AND mfd_data != ''")
        races_with_data = c.fetchone()[0]
        
        conn.close()
        
        log_message(f"📊 Data summary:")
        log_message(f"   - MFD records: {mfd_count}")
        log_message(f"   - Race records: {races_count}")
        log_message(f"   - Races with data: {races_with_data}")
        
        return True
        
    except Exception as e:
        log_message(f"❌ Error checking data integrity: {e}")
        return False

def validate_json_data():
    """Valida que los datos JSON sean correctos"""
    log_message("🔍 Validating JSON data...")
    
    try:
        conn = sqlite3.connect('database/mfd.db')
        c = conn.cursor()
        
        # Verificar MFD JSON
        c.execute("SELECT id, data FROM multi_function_displays WHERE data IS NOT NULL")
        mfd_records = c.fetchall()
        
        invalid_mfd = 0
        for record_id, data in mfd_records:
            try:
                json.loads(data)
            except json.JSONDecodeError:
                invalid_mfd += 1
                log_message(f"⚠️  Invalid JSON in MFD record {record_id}")
        
        # Verificar Race JSON
        c.execute("SELECT id, session_uid, mfd_data FROM races WHERE mfd_data IS NOT NULL AND mfd_data != ''")
        race_records = c.fetchall()
        
        invalid_races = 0
        for record_id, session_uid, data in race_records:
            try:
                json.loads(data)
            except json.JSONDecodeError:
                invalid_races += 1
                log_message(f"⚠️  Invalid JSON in race {record_id} (session {session_uid})")
        
        conn.close()
        
        if invalid_mfd == 0 and invalid_races == 0:
            log_message("✅ All JSON data is valid")
        else:
            log_message(f"⚠️  Found {invalid_mfd} invalid MFD records and {invalid_races} invalid race records")
        
        return invalid_mfd == 0 and invalid_races == 0
        
    except Exception as e:
        log_message(f"❌ Error validating JSON data: {e}")
        return False

def clean_invalid_data():
    """Limpia datos inválidos de la base de datos"""
    log_message("🧹 Cleaning invalid data...")
    
    try:
        conn = sqlite3.connect('database/mfd.db')
        c = conn.cursor()
        
        # Limpiar registros de carreras con JSON inválido
        c.execute("""
            DELETE FROM races 
            WHERE mfd_data IS NOT NULL 
            AND mfd_data != '' 
            AND NOT (
                mfd_data LIKE '{%}' 
                AND mfd_data LIKE '%}'
            )
        """)
        
        deleted_races = c.rowcount
        
        conn.commit()
        conn.close()
        
        if deleted_races > 0:
            log_message(f"🗑️  Removed {deleted_races} invalid race records")
        else:
            log_message("✅ No invalid data found to clean")
        
        return True
        
    except Exception as e:
        log_message(f"❌ Error cleaning invalid data: {e}")
        return False

def optimize_database():
    """Optimiza la base de datos"""
    log_message("⚡ Optimizing database...")
    
    try:
        conn = sqlite3.connect('database/mfd.db')
        
        # Ejecutar VACUUM para optimizar
        conn.execute("VACUUM")
        
        # Analizar estadísticas
        conn.execute("ANALYZE")
        
        conn.close()
        
        log_message("✅ Database optimized")
        return True
        
    except Exception as e:
        log_message(f"❌ Error optimizing database: {e}")
        return False

def create_backup():
    """Crea una copia de seguridad antes de las reparaciones"""
    log_message("💾 Creating backup...")
    
    try:
        import shutil
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"database/mfd_repair_backup_{timestamp}.db"
        shutil.copy2('database/mfd.db', backup_file)
        
        log_message(f"✅ Backup created: {backup_file}")
        return True
        
    except Exception as e:
        log_message(f"❌ Error creating backup: {e}")
        return False

def main():
    """Función principal de reparación"""
    print("=" * 80)
    print("F1 24 Dashboard - Database Repair Tool")
    print("=" * 80)
    print()
    
    # Verificaciones iniciales
    if not check_database_file():
        log_message("❌ Cannot proceed without database file")
        return False
    
    # Crear backup
    if not create_backup():
        log_message("⚠️  Backup failed, continue anyway? (y/N)")
        if input().strip().lower() != 'y':
            return False
    
    # Lista de reparaciones
    repairs = [
        ("Database Structure", check_database_structure, repair_database_structure),
        ("Data Integrity", check_data_integrity, None),
        ("JSON Validation", validate_json_data, clean_invalid_data),
        ("Database Optimization", None, optimize_database),
    ]
    
    # Ejecutar reparaciones
    for repair_name, check_func, repair_func in repairs:
        log_message(f"🔧 {repair_name}...")
        
        needs_repair = False
        
        if check_func:
            if not check_func():
                needs_repair = True
        else:
            needs_repair = True  # Siempre ejecutar si no hay función de verificación
        
        if needs_repair and repair_func:
            if not repair_func():
                log_message(f"❌ {repair_name} repair failed")
                return False
        
        print()
    
    # Verificación final
    log_message("🎯 Final verification...")
    
    if check_database_structure() and check_data_integrity():
        log_message("🎉 Database repair completed successfully!")
        log_message("✅ Your F1 24 Dashboard is ready to use")
        return True
    else:
        log_message("❌ Some issues remain after repair")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("🛑 Repair interrupted by user")
        sys.exit(0)
    except Exception as e:
        log_message(f"❌ Fatal error during repair: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
