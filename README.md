# F1 24 Dashboard - Multi-Functional Display (MFD)

Custom Multi-Functional Display (MFD) for F1 24 with **multiple races support**. Based on the F1-24-Telemetry base repo.

## 🆕 New Features (Multiple Races Update)

- **📊 Multiple Race Storage**: Automatically saves every F1 24 session as a separate race
- **🔍 Historical Race Viewing**: Browse and analyze data from previous races
- **🎯 Advanced Filtering**: Filter races by track, session type, date, and search text
- **🔄 Live/Historical Modes**: Switch between real-time telemetry and historical data
- **💾 Auto-Save**: Races are automatically saved when sessions end
- **🛡️ Robust Error Handling**: System continues running even if individual operations fail

## 📋 System Requirements

- Python 3.8+
- F1 24 game with UDP telemetry enabled
- Web browser (Chrome, Firefox, Safari, Edge)
- ~50MB disk space for race data storage

## 🚀 Quick Start

### 1. First-Time Setup

```bash
# Test system stability
python test_system_stability.py

# Migrate existing data (if you have previous MFD data)
python migrate_existing_data.py

# Repair database if needed
python repair_database.py
```

### 2. Start the System

```bash
# Option A: Use the safe startup script (recommended)
python start_telemetry_server.py

# Option B: Start manually
python telemetry/main.py
```

### 3. Start Web Interface

```bash
# In a separate terminal
python frontend/server.py
```

### 4. Configure F1 24

1. Start F1 24 game
2. Go to **Settings > Telemetry Settings**
3. Set **UDP Telemetry** to **On**
4. Set **IP Address** to **127.0.0.1**
5. Set **Port** to **20777**
6. Set **UDP Format** to **2024**

### 5. Open Dashboard

Open your browser and go to: **http://localhost:5000**

## 🎮 Controller Setup

To navigate the MFD from your controller/steering wheel, map:
- **Custom UDP Action 1**: Navigate Left
- **Custom UDP Action 2**: Navigate Right
- **Custom UDP Action 3**: Toggle Focus Mode

## 🏁 Using Multiple Races Feature

### Live Mode (Default)
- Shows real-time telemetry from your current F1 24 session
- Automatically saves race data when sessions end
- Works exactly like the original system

### Historical Mode
1. Click **"Show Race Selector"** button (top-right corner)
2. Switch to **"Historical Mode"**
3. Use filters to find specific races:
   - **Search**: Find races by name
   - **Track**: Filter by circuit (Monaco, Silverstone, etc.)
   - **Session Type**: Filter by Practice, Qualifying, Race, etc.
   - **Date Range**: Filter by date
4. Click on any race to view its complete telemetry data

### Race Management
- **Auto-Save**: Every F1 24 session is automatically saved
- **Complete Data**: Each race includes full telemetry and timing data
- **Status Tracking**: See which races are completed vs. in-progress
- **Data Preservation**: All your racing history is preserved

## 🛠️ Maintenance & Troubleshooting

### Database Management
```bash
# View all races and database stats
python database_utils.py

# Repair database issues
python repair_database.py

# Test system stability
python test_system_stability.py
```

### Common Issues

**"No races found"**
- Make sure you've completed at least one F1 24 session
- Check that the telemetry server is running
- Verify F1 24 telemetry settings are correct

**"Server not responding"**
- Check if port 20777 is available
- Restart the telemetry server
- Check firewall settings

**"Race data not loading"**
- Use the database repair tool: `python repair_database.py`
- Check browser console for JavaScript errors
- Verify web server is running on port 5000

### Performance Tips
- The system can handle hundreds of races without performance issues
- Database is automatically optimized during repairs
- Old race data can be backed up and archived if needed

## 📁 File Structure

```
f1-24-dashboard/
├── telemetry/
│   ├── main.py              # Main telemetry server (updated)
│   ├── enums/               # F1 24 data enums
│   └── packets/             # Packet parsing logic
├── database/
│   ├── models/
│   │   ├── race.py          # New: Race data model
│   │   └── multi_functional_display.py  # Updated MFD model
│   ├── repositories/
│   │   ├── race_repository.py    # New: Race data management
│   │   └── mfd_repository.py     # Original MFD functions
│   └── mfd.db              # SQLite database
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── RaceSelector.js   # New: Race selection UI
│   │   │   └── ...               # Existing components
│   │   ├── services/
│   │   │   └── api.js            # Updated API functions
│   │   └── App.js                # Updated main app
│   └── server.py                 # Updated web server
├── start_telemetry_server.py    # Safe startup script
├── test_system_stability.py     # System tests
├── repair_database.py           # Database repair tool
├── database_utils.py            # Database management
└── migrate_existing_data.py     # Data migration script
```

## 🔧 Technical Details

### Database Schema
- **races** table: Stores multiple race sessions with metadata
- **multi_function_displays** table: Original MFD data (preserved for compatibility)
- Each race includes complete telemetry data as JSON

### API Endpoints
- `GET /api/mfd` - Current live telemetry (original)
- `GET /api/races` - List all races with optional filters
- `GET /api/races/{id}` - Get specific race data
- `GET /api/tracks` - Available track names
- `GET /api/session-types` - Available session types

### Session Detection
- Automatic detection of new F1 24 sessions via `session_uid` changes
- Session start/end events trigger race creation/completion
- Robust error handling ensures no data loss

## 🤝 Contributing

This is an enhanced version of the original F1-24-Telemetry project with multiple races support.

### Original Project
Based on the excellent work from the F1-24-Telemetry community.

### Enhancements Added
- Multiple race storage and management
- Historical race viewing with advanced filters
- Robust error handling and logging
- Database management tools
- Comprehensive testing suite

## 📄 License

Same license as the original F1-24-Telemetry project.

## 🆘 Support

If you encounter issues:

1. **Run diagnostics**: `python test_system_stability.py`
2. **Check logs**: Look for error messages in the terminal
3. **Repair database**: `python repair_database.py`
4. **Reset if needed**: Backup your database and start fresh

The system is designed to be robust and continue working even if individual operations fail. Your race data is automatically backed up during repairs.
