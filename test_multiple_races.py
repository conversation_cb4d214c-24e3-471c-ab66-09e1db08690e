#!/usr/bin/env python3
"""
Script de pruebas para validar el sistema de múltiples carreras del F1 24 Dashboard
"""

import sys
import json
import sqlite3
import requests
import time
from datetime import datetime

sys.path.append('.')

def test_database_connection():
    """Prueba la conexión a la base de datos"""
    print("🔍 Testing database connection...")
    
    try:
        from database.repositories import init_db, init_races_db
        
        # Inicializar bases de datos
        init_db()
        init_races_db()
        
        # Verificar que las tablas existen
        conn = sqlite3.connect('database/mfd.db')
        c = conn.cursor()
        
        # Verificar tabla original
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='multi_function_displays'")
        mfd_table = c.fetchone()
        
        # Verificar nueva tabla
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='races'")
        races_table = c.fetchone()
        
        conn.close()
        
        if mfd_table and races_table:
            print("✅ Database connection successful - both tables exist")
            return True
        else:
            print("❌ Database tables missing")
            return False
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_race_repository():
    """Prueba las funciones del repositorio de carreras"""
    print("🔍 Testing race repository functions...")
    
    try:
        from database.repositories import save_race, get_all_races, get_race_by_id
        from database.models import Race
        
        # Crear carrera de prueba
        test_race = Race(
            session_uid=123456789,
            track_id=5,
            track_name="Monaco",
            session_type="RACE",
            session_name="Test Race - Monaco",
            timestamp=datetime.now().isoformat(),
            game_mode=3,
            total_laps=78,
            session_duration=7200,
            weather=0,
            track_temperature=35,
            air_temperature=28,
            mfd_data='{"test": "data"}',
            is_completed=True
        )
        
        # Guardar carrera
        race_id = save_race(test_race)
        print(f"✅ Race saved with ID: {race_id}")
        
        # Recuperar carrera
        retrieved_race = get_race_by_id(race_id)
        if retrieved_race and retrieved_race.session_name == test_race.session_name:
            print("✅ Race retrieval successful")
        else:
            print("❌ Race retrieval failed")
            return False
        
        # Listar todas las carreras
        all_races = get_all_races()
        print(f"✅ Found {len(all_races)} total races in database")
        
        return True
        
    except Exception as e:
        print(f"❌ Race repository test failed: {e}")
        return False

def test_api_endpoints():
    """Prueba los endpoints de la API"""
    print("🔍 Testing API endpoints...")
    
    # Nota: Este test asume que el servidor está corriendo en localhost:5000
    base_url = "http://localhost:5000/api"
    
    try:
        # Test endpoint de carreras
        response = requests.get(f"{base_url}/races", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ /api/races endpoint working - found {data.get('total', 0)} races")
        else:
            print(f"❌ /api/races endpoint failed with status {response.status_code}")
            return False
        
        # Test endpoint de tracks
        response = requests.get(f"{base_url}/tracks", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ /api/tracks endpoint working - found {len(data.get('tracks', []))} tracks")
        else:
            print(f"❌ /api/tracks endpoint failed with status {response.status_code}")
            return False
        
        # Test endpoint de session types
        response = requests.get(f"{base_url}/session-types", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ /api/session-types endpoint working - found {len(data.get('session_types', []))} types")
        else:
            print(f"❌ /api/session-types endpoint failed with status {response.status_code}")
            return False
        
        # Test endpoint MFD original
        response = requests.get(f"{base_url}/mfd", timeout=5)
        if response.status_code in [200, 404]:  # 404 es OK si no hay datos
            print("✅ /api/mfd endpoint working (original functionality preserved)")
        else:
            print(f"❌ /api/mfd endpoint failed with status {response.status_code}")
            return False
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("⚠️  API test skipped - server not running")
        print("   Start the server with: python frontend/server.py")
        return True  # No fallar si el servidor no está corriendo
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def test_mfd_model_updates():
    """Prueba las actualizaciones del modelo MultiFunctionDisplay"""
    print("🔍 Testing MultiFunctionDisplay model updates...")
    
    try:
        from database.models import MultiFunctionDisplay
        
        # Crear instancia del MFD
        mfd = MultiFunctionDisplay()
        
        # Verificar que los nuevos campos existen
        required_fields = [
            'session_uid', 'track_id', 'track_name', 'session_name',
            'timestamp', 'game_mode', 'total_laps', 'session_duration'
        ]
        
        for field in required_fields:
            if not hasattr(mfd, field):
                print(f"❌ Missing field: {field}")
                return False
        
        print("✅ MultiFunctionDisplay model has all required new fields")
        
        # Verificar método de obtener nombre del track
        if hasattr(mfd, '_get_track_name'):
            track_name = mfd._get_track_name(5)  # Monaco
            if track_name and track_name != "Unknown Track (5)":
                print(f"✅ Track name mapping working: {track_name}")
            else:
                print("⚠️  Track name mapping may need verification")
        
        return True
        
    except Exception as e:
        print(f"❌ MFD model test failed: {e}")
        return False

def test_data_migration():
    """Prueba que la migración de datos funcione"""
    print("🔍 Testing data migration compatibility...")
    
    try:
        from database.repositories import load_mfd_json
        
        # Intentar cargar datos MFD existentes
        mfd_json = load_mfd_json()
        
        if mfd_json:
            print("✅ Original MFD data loading still works")
            
            # Verificar que es JSON válido
            try:
                data = json.loads(mfd_json)
                print(f"✅ MFD data is valid JSON ({len(mfd_json)} characters)")
            except json.JSONDecodeError:
                print("❌ MFD data is not valid JSON")
                return False
        else:
            print("ℹ️  No original MFD data found (this is normal for new installations)")
        
        return True
        
    except Exception as e:
        print(f"❌ Data migration test failed: {e}")
        return False

def test_frontend_files():
    """Verifica que los archivos del frontend existen"""
    print("🔍 Testing frontend files...")
    
    import os
    
    required_files = [
        'frontend/src/components/RaceSelector.js',
        'frontend/src/services/api.js',
        'frontend/src/App.js'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            return False
    
    # Verificar que RaceSelector tiene el contenido esperado
    try:
        with open('frontend/src/components/RaceSelector.js', 'r') as f:
            content = f.read()
            if 'fetchRaces' in content and 'onRaceSelect' in content:
                print("✅ RaceSelector component has expected functionality")
            else:
                print("❌ RaceSelector component missing expected functionality")
                return False
    except Exception as e:
        print(f"❌ Error reading RaceSelector: {e}")
        return False
    
    return True

def run_all_tests():
    """Ejecuta todas las pruebas"""
    print("=" * 80)
    print("F1 24 Dashboard - Multiple Races System Tests")
    print("=" * 80)
    print()
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Race Repository", test_race_repository),
        ("MFD Model Updates", test_mfd_model_updates),
        ("Data Migration", test_data_migration),
        ("Frontend Files", test_frontend_files),
        ("API Endpoints", test_api_endpoints),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Resumen de resultados
    print("\n" + "=" * 80)
    print("TEST RESULTS SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The multiple races system is ready to use.")
        print("\nNext steps:")
        print("1. Start the telemetry server: python telemetry/main.py")
        print("2. Start the web server: python frontend/server.py")
        print("3. Open http://localhost:5000 in your browser")
        print("4. Start F1 24 and begin racing!")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")
        print("\nRefer to MULTIPLE_RACES_SETUP.md for troubleshooting help.")
    
    return passed == total

if __name__ == "__main__":
    run_all_tests()
