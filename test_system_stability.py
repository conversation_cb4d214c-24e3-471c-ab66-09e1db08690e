#!/usr/bin/env python3
"""
Script para probar la estabilidad del sistema de múltiples carreras
"""

import sys
import json
import time
from datetime import datetime

sys.path.append('.')

def test_database_operations():
    """Prueba las operaciones básicas de la base de datos"""
    print("🔍 Testing database operations...")
    
    try:
        from database.repositories import (
            init_db, init_races_db, save_race, get_all_races, 
            get_race_by_session_uid, create_race_from_mfd
        )
        from database.models import MultiFunctionDisplay, Race
        
        # Inicializar bases de datos
        init_db()
        init_races_db()
        print("✅ Database initialization successful")
        
        # Crear MFD de prueba
        mfd = MultiFunctionDisplay()
        mfd.session_type = "RACE"
        mfd.track_name = "Test Track"
        mfd.session_uid = 12345
        
        # Crear carrera de prueba
        test_race = create_race_from_mfd(mfd, 12345, 0, "Test Track", 3, 50, 3600)
        print("✅ Race creation successful")
        
        # Guardar carrera
        race_id = save_race(test_race)
        if race_id > 0:
            print(f"✅ Race saved with ID: {race_id}")
        else:
            print("❌ Failed to save race")
            return False
        
        # Recuperar carrera
        retrieved_race = get_race_by_session_uid(12345)
        if retrieved_race:
            print("✅ Race retrieval successful")
        else:
            print("❌ Failed to retrieve race")
            return False
        
        # Listar todas las carreras
        all_races = get_all_races()
        print(f"✅ Found {len(all_races)} total races")
        
        return True
        
    except Exception as e:
        print(f"❌ Database operations test failed: {e}")
        return False

def test_mfd_model():
    """Prueba el modelo MultiFunctionDisplay"""
    print("🔍 Testing MultiFunctionDisplay model...")
    
    try:
        from database.models import MultiFunctionDisplay
        
        # Crear instancia
        mfd = MultiFunctionDisplay()
        
        # Verificar campos nuevos
        required_fields = [
            'session_uid', 'track_id', 'track_name', 'session_name',
            'timestamp', 'game_mode', 'total_laps', 'session_duration'
        ]
        
        for field in required_fields:
            if not hasattr(mfd, field):
                print(f"❌ Missing field: {field}")
                return False
        
        print("✅ All required fields present")
        
        # Probar método de nombre de track
        track_name = mfd._get_track_name(0)  # Melbourne
        if track_name:
            print(f"✅ Track name mapping works: {track_name}")
        
        # Probar actualización de nombre de sesión
        mfd.track_name = "Test Track"
        mfd.session_type = "RACE"
        mfd.update_session_name()
        if mfd.session_name:
            print(f"✅ Session name update works: {mfd.session_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ MFD model test failed: {e}")
        return False

def test_json_serialization():
    """Prueba la serialización JSON"""
    print("🔍 Testing JSON serialization...")
    
    try:
        from database.models import MultiFunctionDisplay
        from database.repositories import to_json
        
        # Crear MFD con datos
        mfd = MultiFunctionDisplay()
        mfd.session_type = "RACE"
        mfd.track_name = "Melbourne"
        mfd.session_uid = 98765
        
        # Serializar a JSON
        json_data = to_json(mfd)
        if json_data:
            print(f"✅ JSON serialization successful ({len(json_data)} chars)")
        
        # Verificar que es JSON válido
        parsed_data = json.loads(json_data)
        if parsed_data.get('session_type') == "RACE":
            print("✅ JSON parsing successful")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON serialization test failed: {e}")
        return False

def test_error_handling():
    """Prueba el manejo de errores"""
    print("🔍 Testing error handling...")
    
    try:
        from database.repositories import get_race_by_session_uid, save_race
        from database.models import Race
        
        # Probar con session_uid que no existe
        non_existent_race = get_race_by_session_uid(999999999)
        if non_existent_race is None:
            print("✅ Handles non-existent race correctly")
        
        # Probar con datos inválidos (esto debería manejarse graciosamente)
        try:
            invalid_race = Race(
                session_uid=None,  # Valor inválido
                track_name="Test",
                session_type="RACE"
            )
            # Esto podría fallar, pero no debería crashear el sistema
            save_race(invalid_race)
            print("✅ Error handling works for invalid data")
        except Exception as e:
            print("✅ Error handling works (caught exception gracefully)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def test_session_continuity():
    """Prueba la continuidad de sesiones"""
    print("🔍 Testing session continuity...")
    
    try:
        from database.repositories import save_race, get_race_by_session_uid
        from database.models import Race
        
        # Crear carrera inicial
        session_uid = int(time.time())  # UID único basado en tiempo
        
        race1 = Race(
            session_uid=session_uid,
            track_name="Melbourne",
            session_type="RACE",
            session_name="Test Race 1",
            timestamp=datetime.now().isoformat(),
            mfd_data='{"test": "data1"}',
            is_completed=False
        )
        
        race_id = save_race(race1)
        print(f"✅ Created initial race: {race_id}")
        
        # Actualizar la misma carrera
        race1.mfd_data = '{"test": "updated_data"}'
        race1.session_name = "Test Race 1 - Updated"
        
        updated_race_id = save_race(race1)
        if updated_race_id == race_id:
            print("✅ Race update successful (same ID)")
        
        # Verificar que los datos se actualizaron
        retrieved_race = get_race_by_session_uid(session_uid)
        if retrieved_race and "updated_data" in retrieved_race.mfd_data:
            print("✅ Race data updated correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Session continuity test failed: {e}")
        return False

def run_stability_tests():
    """Ejecuta todas las pruebas de estabilidad"""
    print("=" * 80)
    print("F1 24 Dashboard - System Stability Tests")
    print("=" * 80)
    print()
    
    tests = [
        ("Database Operations", test_database_operations),
        ("MFD Model", test_mfd_model),
        ("JSON Serialization", test_json_serialization),
        ("Error Handling", test_error_handling),
        ("Session Continuity", test_session_continuity),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Resumen de resultados
    print("\n" + "=" * 80)
    print("STABILITY TEST RESULTS")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All stability tests passed! System is ready for production.")
        print("\nRecommendations:")
        print("- Run the telemetry server in background mode")
        print("- Monitor logs for any error messages")
        print("- Backup database regularly")
        print("- Test with actual F1 24 sessions")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please review the errors above.")
        print("\nRecommendations:")
        print("- Fix the failing tests before production use")
        print("- Check database permissions and file access")
        print("- Verify all dependencies are installed")
    
    return passed == total

if __name__ == "__main__":
    success = run_stability_tests()
    sys.exit(0 if success else 1)
