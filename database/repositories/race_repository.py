import sqlite3
import json
from typing import List, Optional
from datetime import datetime
from ..models.race import Race
from ..models import MultiFunctionDisplay

_DB_FILE = 'database/mfd.db'

# SQL Statements para la tabla de carreras
_CREATE_RACES_TABLE = '''CREATE TABLE IF NOT EXISTS races (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_uid TEXT NOT NULL,
    track_id INTEGER,
    track_name TEXT,
    session_type TEXT,
    session_name TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    game_mode INTEGER,
    total_laps INTEGER,
    session_duration INTEGER,
    weather INTEGER,
    track_temperature INTEGER,
    air_temperature INTEGER,
    mfd_data TEXT,
    is_completed BOOLEAN DEFAULT FALSE,
    UNIQUE(session_uid)
)'''

_INSERT_RACE = '''INSERT OR REPLACE INTO races (
    session_uid, track_id, track_name, session_type, session_name,
    timestamp, game_mode, total_laps, session_duration, weather,
    track_temperature, air_temperature, mfd_data, is_completed
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)'''

_UPDATE_RACE = '''UPDATE races SET 
    track_id=?, track_name=?, session_type=?, session_name=?,
    game_mode=?, total_laps=?, session_duration=?, weather=?,
    track_temperature=?, air_temperature=?, mfd_data=?, is_completed=?
    WHERE session_uid=?'''

_SELECT_ALL_RACES = '''SELECT * FROM races ORDER BY timestamp DESC'''

_SELECT_RACE_BY_ID = '''SELECT * FROM races WHERE id=?'''

_SELECT_RACE_BY_SESSION_UID = '''SELECT * FROM races WHERE session_uid=?'''

_SELECT_RACES_BY_FILTERS = '''SELECT * FROM races WHERE 
    (? IS NULL OR track_name LIKE ?) AND
    (? IS NULL OR session_type = ?) AND
    (? IS NULL OR date(timestamp) >= date(?)) AND
    (? IS NULL OR date(timestamp) <= date(?))
    ORDER BY timestamp DESC'''

def to_json(obj) -> str:
    """Convierte objeto a JSON"""
    return json.dumps(obj, default=lambda o: o.__dict__)

def init_races_db():
    """Inicializa la tabla de carreras"""
    conn = sqlite3.connect(_DB_FILE)
    c = conn.cursor()
    c.execute(_CREATE_RACES_TABLE)
    conn.commit()
    conn.close()

def save_race(race: Race) -> int:
    """
    Guarda o actualiza una carrera en la base de datos
    Returns: ID de la carrera guardada
    """
    try:
        conn = sqlite3.connect(_DB_FILE)
        c = conn.cursor()

        # Verificar si ya existe una carrera con este session_uid
        c.execute(_SELECT_RACE_BY_SESSION_UID, (str(race.session_uid),))
        existing_race = c.fetchone()

        if existing_race:
            # Actualizar carrera existente
            c.execute(_UPDATE_RACE, (
                race.track_id, race.track_name, race.session_type, race.session_name,
                race.game_mode, race.total_laps, race.session_duration, race.weather,
                race.track_temperature, race.air_temperature, race.mfd_data, race.is_completed,
                str(race.session_uid)
            ))
            race_id = existing_race[0]
        else:
            # Insertar nueva carrera
            c.execute(_INSERT_RACE, (
                str(race.session_uid), race.track_id, race.track_name, race.session_type, race.session_name,
                race.timestamp, race.game_mode, race.total_laps, race.session_duration, race.weather,
                race.track_temperature, race.air_temperature, race.mfd_data, race.is_completed
            ))
            race_id = c.lastrowid

        conn.commit()
        conn.close()
        return race_id
    except Exception as e:
        print(f"❌ Error saving race: {e}")
        try:
            if 'conn' in locals():
                conn.close()
        except:
            pass
        return 0

def get_all_races() -> List[Race]:
    """Obtiene todas las carreras ordenadas por fecha descendente"""
    conn = sqlite3.connect(_DB_FILE)
    c = conn.cursor()
    c.execute(_SELECT_ALL_RACES)
    rows = c.fetchall()
    conn.close()
    
    races = []
    for row in rows:
        race = _row_to_race(row)
        races.append(race)
    
    return races

def get_race_by_id(race_id: int) -> Optional[Race]:
    """Obtiene una carrera por su ID"""
    conn = sqlite3.connect(_DB_FILE)
    c = conn.cursor()
    c.execute(_SELECT_RACE_BY_ID, (race_id,))
    row = c.fetchone()
    conn.close()
    
    if row:
        return _row_to_race(row)
    return None

def get_race_by_session_uid(session_uid: int) -> Optional[Race]:
    """Obtiene una carrera por su session_uid"""
    try:
        conn = sqlite3.connect(_DB_FILE)
        c = conn.cursor()
        c.execute(_SELECT_RACE_BY_SESSION_UID, (str(session_uid),))
        row = c.fetchone()
        conn.close()

        if row:
            return _row_to_race(row)
        return None
    except Exception as e:
        print(f"❌ Error getting race by session_uid: {e}")
        try:
            if 'conn' in locals():
                conn.close()
        except:
            pass
        return None

def get_races_by_filters(track_name: Optional[str] = None, 
                        session_type: Optional[str] = None,
                        date_from: Optional[str] = None,
                        date_to: Optional[str] = None) -> List[Race]:
    """Obtiene carreras filtradas por criterios específicos"""
    conn = sqlite3.connect(_DB_FILE)
    c = conn.cursor()
    
    # Preparar parámetros para la consulta
    track_param = f"%{track_name}%" if track_name else None
    
    c.execute(_SELECT_RACES_BY_FILTERS, (
        track_name, track_param,
        session_type, session_type,
        date_from, date_from,
        date_to, date_to
    ))
    
    rows = c.fetchall()
    conn.close()
    
    races = []
    for row in rows:
        race = _row_to_race(row)
        races.append(race)
    
    return races

def mark_race_completed(session_uid: int):
    """Marca una carrera como completada"""
    conn = sqlite3.connect(_DB_FILE)
    c = conn.cursor()
    c.execute('UPDATE races SET is_completed=TRUE WHERE session_uid=?', (str(session_uid),))
    conn.commit()
    conn.close()

def _row_to_race(row) -> Race:
    """Convierte una fila de la base de datos a un objeto Race"""
    return Race(
        id=row[0],
        session_uid=row[1],
        track_id=row[2],
        track_name=row[3],
        session_type=row[4],
        session_name=row[5],
        timestamp=row[6],
        game_mode=row[7],
        total_laps=row[8],
        session_duration=row[9],
        weather=row[10],
        track_temperature=row[11],
        air_temperature=row[12],
        mfd_data=row[13],
        is_completed=bool(row[14])
    )

def create_race_from_mfd(mfd: MultiFunctionDisplay, session_uid: int, 
                        track_id: int = -1, track_name: str = "", 
                        game_mode: int = 0, total_laps: int = 0, 
                        session_duration: int = 0) -> Race:
    """Crea un objeto Race desde un MultiFunctionDisplay"""
    return Race(
        session_uid=session_uid,
        track_id=track_id,
        track_name=track_name,
        session_type=mfd.session_type,
        timestamp=datetime.now().isoformat(),
        game_mode=game_mode,
        total_laps=total_laps,
        session_duration=session_duration,
        weather=0,  # Se actualizará desde weather_panel si está disponible
        track_temperature=0,
        air_temperature=0,
        mfd_data=to_json(mfd),
        is_completed=False
    )
