#!/usr/bin/env python3
"""
Script de migración para convertir datos existentes del MFD a la nueva estructura de múltiples carreras.
Este script debe ejecutarse una sola vez después de implementar el nuevo sistema.
"""

import sys
import json
from datetime import datetime

sys.path.append('.')

from database.repositories import (
    init_db, init_races_db, load_mfd_json, 
    save_race, create_race_from_mfd
)
from database.models import MultiFunctionDisplay, Race

def migrate_existing_mfd_data():
    """
    Migra los datos existentes del MFD a la nueva estructura de carreras múltiples
    """
    print("Starting migration of existing MFD data...")
    
    # Inicializar bases de datos
    init_db()
    init_races_db()
    
    # Cargar datos existentes del MFD
    mfd_json = load_mfd_json()
    
    if not mfd_json:
        print("No existing MFD data found. Migration not needed.")
        return
    
    try:
        # Parsear datos JSON
        mfd_data = json.loads(mfd_json)
        print(f"Found existing MFD data: {len(mfd_json)} characters")
        
        # Crear objeto MultiFunctionDisplay desde los datos
        mfd = MultiFunctionDisplay()
        
        # Mapear campos básicos
        if 'active_panel_index' in mfd_data:
            mfd.active_panel_index = mfd_data['active_panel_index']
        if 'focus_mode' in mfd_data:
            mfd.focus_mode = mfd_data['focus_mode']
        if 'session_type' in mfd_data:
            mfd.session_type = mfd_data['session_type']
        if 'pit_speed_limit' in mfd_data:
            mfd.pit_speed_limit = mfd_data['pit_speed_limit']
        if 'ers_deploy_mode' in mfd_data:
            mfd.ers_deploy_mode = mfd_data['ers_deploy_mode']
        if 'brake_bias' in mfd_data:
            mfd.brake_bias = mfd_data['brake_bias']
        if 'differential' in mfd_data:
            mfd.differential = mfd_data['differential']
        
        # Generar metadatos de sesión por defecto
        session_uid = 999999  # UID especial para datos migrados
        track_name = "Unknown Track"
        session_name = f"Migrated Session - {mfd.session_type or 'Unknown'}"
        
        # Intentar extraer información adicional de los paneles si está disponible
        if 'panels' in mfd_data and len(mfd_data['panels']) > 0:
            # Buscar información en el panel de clima
            weather_panel = None
            if len(mfd_data['panels']) > 4:
                weather_panel = mfd_data['panels'][4]
            
            # Buscar información en el panel de leaderboard para obtener datos del circuito
            leaderboard_panel = None
            if len(mfd_data['panels']) > 3:
                leaderboard_panel = mfd_data['panels'][3]
        
        # Crear objeto Race
        race = Race(
            session_uid=session_uid,
            track_id=-1,  # Desconocido
            track_name=track_name,
            session_type=mfd.session_type or "UNKNOWN",
            session_name=session_name,
            timestamp=datetime.now().isoformat(),
            game_mode=0,  # Desconocido
            total_laps=0,  # Desconocido
            session_duration=0,  # Desconocido
            weather=0,  # Desconocido
            track_temperature=0,  # Desconocido
            air_temperature=0,  # Desconocido
            mfd_data=mfd_json,  # Datos originales completos
            is_completed=True  # Marcar como completada ya que son datos históricos
        )
        
        # Guardar la carrera migrada
        race_id = save_race(race)
        
        print(f"✅ Successfully migrated existing MFD data to race ID: {race_id}")
        print(f"   Session Name: {race.session_name}")
        print(f"   Session Type: {race.session_type}")
        print(f"   Timestamp: {race.timestamp}")
        print(f"   Data Size: {len(race.mfd_data)} characters")
        
        return race_id
        
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing existing MFD data: {e}")
        return None
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        return None

def verify_migration():
    """
    Verifica que la migración se haya completado correctamente
    """
    print("\nVerifying migration...")
    
    try:
        from database.repositories import get_all_races
        
        races = get_all_races()
        print(f"Total races in database: {len(races)}")
        
        # Buscar la carrera migrada
        migrated_races = [race for race in races if race.session_uid == 999999]
        
        if migrated_races:
            print(f"✅ Found {len(migrated_races)} migrated race(s)")
            for race in migrated_races:
                print(f"   - ID: {race.id}, Name: {race.session_name}")
        else:
            print("ℹ️  No migrated races found (this is normal if no previous data existed)")
            
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

def main():
    """
    Función principal del script de migración
    """
    print("=" * 60)
    print("F1 24 Dashboard - Data Migration Script")
    print("=" * 60)
    print()
    
    # Confirmar antes de proceder
    response = input("This will migrate existing MFD data to the new multiple races structure.\nDo you want to continue? (y/N): ")
    
    if response.lower() != 'y':
        print("Migration cancelled.")
        return
    
    print()
    
    # Ejecutar migración
    race_id = migrate_existing_mfd_data()
    
    if race_id:
        # Verificar migración
        verify_migration()
        
        print()
        print("=" * 60)
        print("✅ Migration completed successfully!")
        print("=" * 60)
        print()
        print("Next steps:")
        print("1. Start the telemetry server: python telemetry/main.py")
        print("2. Start the web server: python frontend/server.py")
        print("3. Open your browser and test the new race selection features")
        print()
        print("The migrated data will appear in the Historical Mode.")
    else:
        print()
        print("=" * 60)
        print("ℹ️  Migration completed (no data to migrate)")
        print("=" * 60)
        print()
        print("You can now start using the new multiple races system:")
        print("1. Start the telemetry server: python telemetry/main.py")
        print("2. Start the web server: python frontend/server.py")
        print("3. Start F1 24 and begin racing to create new race data")

if __name__ == "__main__":
    main()
