#!/usr/bin/env python3
"""
Script para corregir el tipo de datos de session_uid en la base de datos
"""

import sys
import sqlite3
import os
from datetime import datetime

sys.path.append('.')

def log_message(message):
    """Log message with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_database_exists():
    """Verifica que la base de datos exista"""
    db_file = 'database/mfd.db'
    if not os.path.exists(db_file):
        log_message("❌ Database file does not exist")
        return False
    return True

def backup_database():
    """Crea una copia de seguridad de la base de datos"""
    try:
        import shutil
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"database/mfd_backup_session_uid_fix_{timestamp}.db"
        shutil.copy2('database/mfd.db', backup_file)
        log_message(f"✅ Backup created: {backup_file}")
        return True
    except Exception as e:
        log_message(f"❌ Error creating backup: {e}")
        return False

def check_session_uid_type():
    """Verifica el tipo de datos actual de session_uid"""
    try:
        conn = sqlite3.connect('database/mfd.db')
        c = conn.cursor()
        
        # Verificar si la tabla races existe
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='races'")
        if not c.fetchone():
            log_message("ℹ️  Races table does not exist yet")
            conn.close()
            return "not_exists"
        
        # Obtener información de la columna session_uid
        c.execute("PRAGMA table_info(races)")
        columns = c.fetchall()
        
        session_uid_type = None
        for column in columns:
            if column[1] == 'session_uid':  # column[1] es el nombre
                session_uid_type = column[2]  # column[2] es el tipo
                break
        
        conn.close()
        
        if session_uid_type:
            log_message(f"📊 Current session_uid type: {session_uid_type}")
            return session_uid_type.upper()
        else:
            log_message("❌ session_uid column not found")
            return "not_found"
            
    except Exception as e:
        log_message(f"❌ Error checking session_uid type: {e}")
        return "error"

def migrate_session_uid_to_text():
    """Migra la columna session_uid de INTEGER a TEXT"""
    try:
        conn = sqlite3.connect('database/mfd.db')
        c = conn.cursor()
        
        log_message("🔄 Starting session_uid migration...")
        
        # Paso 1: Crear tabla temporal con la estructura correcta
        c.execute('''CREATE TABLE races_temp (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_uid TEXT NOT NULL,
            track_id INTEGER,
            track_name TEXT,
            session_type TEXT,
            session_name TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            game_mode INTEGER,
            total_laps INTEGER,
            session_duration INTEGER,
            weather INTEGER,
            track_temperature INTEGER,
            air_temperature INTEGER,
            mfd_data TEXT,
            is_completed BOOLEAN DEFAULT FALSE,
            UNIQUE(session_uid)
        )''')
        
        # Paso 2: Copiar datos existentes, convirtiendo session_uid a TEXT
        c.execute('''INSERT INTO races_temp 
                     SELECT id, CAST(session_uid AS TEXT), track_id, track_name, session_type, 
                            session_name, timestamp, game_mode, total_laps, session_duration,
                            weather, track_temperature, air_temperature, mfd_data, is_completed
                     FROM races''')
        
        # Paso 3: Eliminar tabla original
        c.execute('DROP TABLE races')
        
        # Paso 4: Renombrar tabla temporal
        c.execute('ALTER TABLE races_temp RENAME TO races')
        
        conn.commit()
        conn.close()
        
        log_message("✅ session_uid migration completed successfully")
        return True
        
    except Exception as e:
        log_message(f"❌ Error during migration: {e}")
        try:
            conn.rollback()
            conn.close()
        except:
            pass
        return False

def verify_migration():
    """Verifica que la migración se haya completado correctamente"""
    try:
        conn = sqlite3.connect('database/mfd.db')
        c = conn.cursor()
        
        # Verificar tipo de session_uid
        c.execute("PRAGMA table_info(races)")
        columns = c.fetchall()
        
        session_uid_type = None
        for column in columns:
            if column[1] == 'session_uid':
                session_uid_type = column[2]
                break
        
        # Contar registros
        c.execute("SELECT COUNT(*) FROM races")
        count = c.fetchone()[0]
        
        conn.close()
        
        if session_uid_type and session_uid_type.upper() == 'TEXT':
            log_message(f"✅ Migration verified: session_uid is now TEXT ({count} records)")
            return True
        else:
            log_message(f"❌ Migration verification failed: session_uid type is {session_uid_type}")
            return False
            
    except Exception as e:
        log_message(f"❌ Error verifying migration: {e}")
        return False

def test_large_session_uid():
    """Prueba insertar un session_uid grande"""
    try:
        from database.repositories import save_race
        from database.models import Race
        
        # Crear carrera de prueba con session_uid grande
        large_session_uid = 12816137889522929800
        test_race = Race(
            session_uid=large_session_uid,
            track_name="Test Track",
            session_type="TEST",
            session_name="Large Session UID Test",
            timestamp=datetime.now().isoformat(),
            mfd_data='{"test": "large_session_uid"}',
            is_completed=True
        )
        
        race_id = save_race(test_race)
        if race_id > 0:
            log_message(f"✅ Large session_uid test successful (race ID: {race_id})")
            
            # Limpiar datos de prueba
            conn = sqlite3.connect('database/mfd.db')
            c = conn.cursor()
            c.execute('DELETE FROM races WHERE session_uid = ?', (str(large_session_uid),))
            conn.commit()
            conn.close()
            log_message("🧹 Test data cleaned up")
            
            return True
        else:
            log_message("❌ Large session_uid test failed")
            return False
            
    except Exception as e:
        log_message(f"❌ Error testing large session_uid: {e}")
        return False

def main():
    """Función principal"""
    print("=" * 80)
    print("F1 24 Dashboard - Session UID Type Fix")
    print("=" * 80)
    print()
    
    # Verificar que la base de datos exista
    if not check_database_exists():
        log_message("❌ Cannot proceed without database file")
        return False
    
    # Verificar tipo actual
    current_type = check_session_uid_type()
    
    if current_type == "not_exists":
        log_message("ℹ️  Races table doesn't exist yet - will be created with correct type")
        return True
    elif current_type == "TEXT":
        log_message("✅ session_uid is already TEXT type")
        # Probar con session_uid grande
        return test_large_session_uid()
    elif current_type == "INTEGER":
        log_message("⚠️  session_uid is INTEGER type - migration needed")
        
        # Crear backup
        if not backup_database():
            log_message("⚠️  Backup failed, continue anyway? (y/N)")
            if input().strip().lower() != 'y':
                return False
        
        # Ejecutar migración
        if migrate_session_uid_to_text():
            if verify_migration():
                log_message("🎉 Migration completed successfully!")
                return test_large_session_uid()
            else:
                log_message("❌ Migration verification failed")
                return False
        else:
            log_message("❌ Migration failed")
            return False
    else:
        log_message(f"❌ Unexpected session_uid type: {current_type}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            log_message("🎯 Session UID type fix completed successfully!")
            log_message("✅ Your F1 24 Dashboard can now handle large session UIDs")
        else:
            log_message("❌ Session UID type fix failed")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("🛑 Fix interrupted by user")
        sys.exit(0)
    except Exception as e:
        log_message(f"❌ Fatal error during fix: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
