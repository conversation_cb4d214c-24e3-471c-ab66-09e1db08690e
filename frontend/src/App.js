import React, { useEffect, useState } from 'react';
import { fetchMFDData, fetchRaceById } from './services/api';
import Navigation from './components/Navigation';
import TimingsPanel from './components/TimingsPanel';
import StrategyPanel from './components/StrategyPanel';
import TyreInfoPanel from './components/TyreInfoPanel';
import LeaderboardPanel from './components/LeaderboardPanel';
import WeatherPanel from './components/WeatherPanel';
import DamagePanel from './components/DamagePanel';
import FocusPanel from 'components/FocusPanel';
import RaceSelector from './components/RaceSelector';

function App() {
	const [mfdData, setMfdData] = useState(null);
	const [mode, setMode] = useState('live'); // 'live' or 'historical'
	const [selectedRace, setSelectedRace] = useState(null);
	const [showRaceSelector, setShowRaceSelector] = useState(false);

	// Fetch live data every 200ms when in live mode
	useEffect(() => {
		if (mode === 'live') {
			const interval = setInterval(async () => {
				const data = await fetchMFDData();
				setMfdData(data);
			}, 200);

			return () => clearInterval(interval); // Clean up interval on component unmount
		}
	}, [mode]);

	// Handle mode change
	const handleModeChange = (newMode) => {
		setMode(newMode);
		if (newMode === 'live') {
			setSelectedRace(null);
		}
	};

	// Handle race selection
	const handleRaceSelect = async (race) => {
		setSelectedRace(race);
		if (race && race.mfd_data) {
			// If race already has MFD data, use it directly
			setMfdData(JSON.parse(race.mfd_data));
		} else {
			// Fetch full race data including MFD data
			const fullRaceData = await fetchRaceById(race.id);
			if (fullRaceData && fullRaceData.mfd_data) {
				setMfdData(fullRaceData.mfd_data);
			}
		}
	};

	// Show loading state
	if (!mfdData || !mfdData.panels) {
		return (
			<div className="flex items-center justify-center h-screen bg-mainDark text-mainWhite">
				<div className="text-center">
					<div className="text-xl mb-4">
						{mode === 'live' ? 'Waiting for telemetry data...' : 'Loading race data...'}
					</div>
					<div className="mb-4">
						<button
							onClick={() => setShowRaceSelector(!showRaceSelector)}
							className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
						>
							{showRaceSelector ? 'Hide' : 'Show'} Race Selector
						</button>
					</div>
					{showRaceSelector && (
						<div className="max-w-4xl mx-auto">
							<RaceSelector
								onRaceSelect={handleRaceSelect}
								onModeChange={handleModeChange}
								currentMode={mode}
								selectedRace={selectedRace}
							/>
						</div>
					)}
				</div>
			</div>
		);
	}

	const activePanel = mfdData.panels[mfdData.active_panel_index] || null;

	const renderActivePanel = () => {
		switch (mfdData.active_panel_index) {
			case 0: return <TimingsPanel data={activePanel} sessionType={mfdData.session_type} />;
			case 1: return <TyreInfoPanel data={activePanel} teamName={mfdData.player ? mfdData.player.team : ''} />;
			case 2: return <StrategyPanel data={activePanel} />;
			case 3: return <LeaderboardPanel data={activePanel.drivers} />;
			case 4: return <WeatherPanel data={activePanel} />;
			case 5: return <DamagePanel data={activePanel} />;
			default: return null;
		}
	};

	return (
		<div className="h-screen w-screen bg-mainDark text-mainWhite overflow-hidden">
			{/* Race Selector Toggle */}
			<div className="absolute top-4 right-4 z-50">
				<button
					onClick={() => setShowRaceSelector(!showRaceSelector)}
					className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 shadow-lg"
				>
					{showRaceSelector ? 'Hide' : 'Show'} Race Selector
				</button>
			</div>

			{/* Race Selector */}
			{showRaceSelector && (
				<div className="absolute top-16 right-4 z-40 w-96 max-h-96 overflow-hidden">
					<RaceSelector
						onRaceSelect={handleRaceSelect}
						onModeChange={handleModeChange}
						currentMode={mode}
						selectedRace={selectedRace}
					/>
				</div>
			)}

			{/* Mode Indicator */}
			<div className="absolute top-4 left-4 z-50">
				<div className={`px-4 py-2 rounded shadow-lg ${
					mode === 'live' ? 'bg-green-600' : 'bg-blue-600'
				}`}>
					<span className="font-semibold">
						{mode === 'live' ? '🔴 LIVE' : '📊 HISTORICAL'}
					</span>
					{mode === 'historical' && selectedRace && (
						<div className="text-xs mt-1">
							{selectedRace.session_name}
						</div>
					)}
				</div>
			</div>

			{/* Main Content */}
			<div className="inline-flex grow place-content-center h-full w-full pt-4 pb-12">
				{mfdData.focus_mode ? (
					<FocusPanel
						generalData={mfdData}
						timingsData={mfdData.panels[0]}
						tyreData={mfdData.panels[1]}
						strategyData={mfdData.panels[2]}
						weatherData={mfdData.panels[4]}
					/>
				) : (
					<div className="flex grow flex-col justify-start">
						{/* Navigation */}
						<div className="flex flex-col justify-stretch items-stretch pt-2 pb-6 px-16 border-b-2 border-b-mainBorder/25">
							<Navigation panels={mfdData.panels} activePanelIndex={mfdData.active_panel_index} />
						</div>

						{/* Current Settings */}
						<div className="flex justify-stretch items-center py-2 px-32 border-b-2 shadow-inner rounded-xl bg-mainLight/50 border-mainBorder/25">
							<span className="text-center text-xl font-bold">ERS Mode: <span className="capitalize">{mfdData.ers_deploy_mode}</span></span>
							<span className="text-center text-xl font-bold">Differential: {mfdData.differential_pct}%</span>
							<span className="text-center text-xl font-bold">Brake Bias: {mfdData.front_brake_bias}%</span>
							<span className="text-center text-xl font-bold">Lap Time: <span className={`${mfdData.current_lap_invalid ? 'text-mainRed' : 'text-mainWhite/25'}`}>Invalid</span></span>
						</div>

						{/* Active Panel */}
						<div className="flex grow flex-col justify-stretch content-stretch shadow-inner rounded-xl p-4 border-b-2 bg-mainLight shadow-mainDark border-y-mainBorder/25">
							{renderActivePanel()}
						</div>
					</div>
				)}
			</div>
		</div>
	);
}

export default App;
