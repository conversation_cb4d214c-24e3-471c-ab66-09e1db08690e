import socket, sys, json
import traceback
from datetime import datetime

sys.path.append('.')

from database.models import *
from database.repositories import (
    init_db, save_mfd, init_races_db, save_race,
    get_race_by_session_uid, mark_race_completed, create_race_from_mfd
)

from enums import PacketIDs, EventCodes

from packets import *

_PORT = 20777
_IP = '127.0.0.1'

sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
sock.bind((_IP, _PORT))

# MDF Panels
multi_function_display = MultiFunctionDisplay()

# Session tracking variables
current_session_uid = 0
current_race = None

def to_json(obj):
    return json.dumps(obj, default=lambda o: o.__dict__)

def handle_session_change(new_session_uid: int):
    """Maneja el cambio de sesión de forma segura"""
    global current_session_uid, current_race, multi_function_display

    if current_session_uid != 0 and current_session_uid != new_session_uid:
        # Guardar la sesión anterior como carrera completa
        if current_race:
            try:
                current_race.is_completed = True
                current_race.mfd_data = to_json(multi_function_display)
                save_race(current_race)
                print(f"✅ Saved completed race: {current_race.session_name}")
            except Exception as e:
                print(f"❌ Error saving completed race: {e}")

    # Actualizar session_uid actual
    current_session_uid = new_session_uid

    # Verificar si ya existe una carrera para esta sesión
    try:
        existing_race = get_race_by_session_uid(new_session_uid)
        if existing_race:
            current_race = existing_race
            print(f"📂 Resumed existing race: {current_race.session_name}")
        else:
            # Crear nueva carrera solo si no existe
            current_race = None  # Se creará cuando tengamos datos de sesión
            print(f"🆕 New session detected: {new_session_uid}")
    except Exception as e:
        print(f"❌ Error checking existing race: {e}")
        current_race = None

def handle_session_started_event():
    """Maneja el evento SESSION_STARTED"""
    global current_race, multi_function_display

    if current_session_uid != 0:
        try:
            # Solo crear nueva carrera si no existe una para esta sesión
            if not current_race or current_race.session_uid != current_session_uid:
                current_race = create_race_from_mfd(
                    multi_function_display,
                    current_session_uid,
                    multi_function_display.track_id,
                    multi_function_display.track_name,
                    multi_function_display.game_mode,
                    multi_function_display.total_laps,
                    multi_function_display.session_duration
                )
                save_race(current_race)
                print(f"🏁 Started new race: {current_race.session_name}")
            else:
                print(f"🔄 Continuing existing race: {current_race.session_name}")
        except Exception as e:
            print(f"❌ Error handling session start: {e}")

def handle_session_ended_event():
    """Maneja el evento SESSION_ENDED"""
    global current_race, multi_function_display

    if current_race:
        try:
            current_race.is_completed = True
            current_race.mfd_data = to_json(multi_function_display)
            save_race(current_race)
            print(f"🏆 Completed race: {current_race.session_name}")
        except Exception as e:
            print(f"❌ Error completing race: {e}")

def update_current_race():
    """Actualiza la carrera actual con los datos del MFD"""
    global current_race, multi_function_display

    try:
        # Crear carrera si no existe y tenemos session_uid válido
        if not current_race and current_session_uid != 0:
            current_race = create_race_from_mfd(
                multi_function_display,
                current_session_uid,
                multi_function_display.track_id,
                multi_function_display.track_name,
                multi_function_display.game_mode,
                multi_function_display.total_laps,
                multi_function_display.session_duration
            )
            print(f"🆕 Created new race: {current_race.session_name}")

        # Actualizar carrera existente
        if current_race and current_session_uid != 0:
            current_race.mfd_data = to_json(multi_function_display)
            current_race.track_name = multi_function_display.track_name
            current_race.session_type = multi_function_display.session_type
            current_race.total_laps = multi_function_display.total_laps
            current_race.session_duration = multi_function_display.session_duration

            # Actualizar datos del clima si están disponibles
            try:
                weather_panel = multi_function_display.weather_panel()
                if hasattr(weather_panel, 'temperature_air'):
                    current_race.air_temperature = weather_panel.temperature_air
                if hasattr(weather_panel, 'temperature_track'):
                    current_race.track_temperature = weather_panel.temperature_track
                if hasattr(weather_panel, 'weather'):
                    current_race.weather = weather_panel.weather
            except Exception as e:
                # No es crítico si falla la actualización del clima
                pass

            # Actualizar nombre de sesión
            try:
                multi_function_display.update_session_name()
                current_race.session_name = multi_function_display.session_name
            except Exception as e:
                # No es crítico si falla la actualización del nombre
                pass

            save_race(current_race)
    except Exception as e:
        print(f"❌ Error updating current race: {e}")

def log_message(message):
	"""Log message with timestamp"""
	timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
	print(f"[{timestamp}] {message}")

def start_server():
	log_message(f"🚀 Starting F1 24 Dashboard Telemetry Server on port {_PORT}...")
	log_message("📊 Multiple races support enabled")

	packet_count = 0
	error_count = 0

	while True:
		try:
			data, _ = sock.recvfrom(2048)
			handle_packet(data)
			packet_count += 1

			# Log progress every 1000 packets
			if packet_count % 1000 == 0:
				log_message(f"📈 Processed {packet_count} packets (errors: {error_count})")

		except KeyboardInterrupt:
			log_message("🛑 Server stopped by user")
			break
		except Exception as e:
			error_count += 1
			log_message(f"❌ Error processing packet: {e}")
			if error_count % 10 == 0:  # Log every 10th error to avoid spam
				log_message(f"⚠️  Total errors: {error_count}")
			continue

def handle_packet(data: bytes):
	try:
		if len(data) >= PACKET_HEADER_FORMAT_SIZE:
				packet_header_bytes = data[:PACKET_HEADER_FORMAT_SIZE]
				packet_header = unpack_packet_header(packet_header_bytes)

				# Detectar cambio de sesión
				if packet_header.session_uid != current_session_uid:
					handle_session_change(packet_header.session_uid)

				remaning_bytes = data[PACKET_HEADER_FORMAT_SIZE:]

				match packet_header.packet_id:
				case PacketIDs.CAR_TELEMETRY.value:
					car_telemetry_packet = unpack_car_telemetry(packet_header, remaning_bytes)
					
					multi_function_display.update_from_car_telemetry_packet(car_telemetry_packet)

				case PacketIDs.CAR_STATUS.value:
					car_status_packet = unpack_car_status(packet_header, remaning_bytes)

					multi_function_display.update_from_car_status_packet(car_status_packet)

				case PacketIDs.CAR_DAMAGE.value:
					car_damage_packet = unpack_car_damage(packet_header, remaning_bytes)

					multi_function_display.update_from_car_damage_packet(car_damage_packet)

				case PacketIDs.TYRE_SETS.value:
					tyre_sets_packet = unpack_tyre_sets(packet_header, remaning_bytes)

					multi_function_display.update_from_tyre_sets_packet(tyre_sets_packet)
				
				case PacketIDs.LAP_DATA.value:
					lap_data_packet = unpack_lap_data(packet_header, remaning_bytes)

					multi_function_display.update_from_lap_data_packet(lap_data_packet)

				case PacketIDs.PARTICIPANTS.value:
					participants_packet = unpack_participants(packet_header, remaning_bytes)

					multi_function_display.update_from_participants_packet(participants_packet)

				case PacketIDs.SESSION.value:
					session_packet = unpack_session(packet_header, remaning_bytes)

					multi_function_display.update_from_session_packet(session_packet)

				case PacketIDs.EVENT.value:
					event_packet = unpack_event_packet(packet_header, remaning_bytes)
					event = event_packet.event

					# Convertir event_string_code a string para comparar
					event_code = ''.join(chr(b) for b in event_packet.event_string_code)

					# Session Events
					if event_code == EventCodes.SESSION_STARTED.value:
						handle_session_started_event()
					elif event_code == EventCodes.SESSION_ENDED.value:
						handle_session_ended_event()
					elif event_code == EventCodes.CHEQUERED_FLAG.value:
						handle_session_ended_event()

					# Button Events
					elif type(event) == ButtonsEvent:
						if (event.button_status & ButtonFlags.UDP_ACTION_1):
							multi_function_display.navigate_left()

						elif (event.button_status & ButtonFlags.UDP_ACTION_2):
							multi_function_display.navigate_right()

						elif (event.button_status & ButtonFlags.UDP_ACTION_3):
							multi_function_display.focus_mode_toggle()

					elif type(event) == SafetyCarEvent:
						multi_function_display.update_from_safety_car_event(event)

					elif type(event) == FastestLapEvent:
						multi_function_display.update_from_fastest_lap_event(event)

			# Guardar MFD (para compatibilidad con sistema existente)
			try:
				save_mfd(multi_function_display)
			except Exception as e:
				log_message(f"❌ Error saving MFD: {e}")

			# Actualizar carrera actual si existe
			try:
				update_current_race()
			except Exception as e:
				log_message(f"❌ Error updating current race: {e}")

	except Exception as e:
		log_message(f"❌ Error handling packet: {e}")
		# Continue processing other packets

if __name__ == '__main__':
	try:
		init_db()
		init_races_db()
		log_message("✅ Database initialized for multiple races support")
		start_server()
	except Exception as e:
		log_message(f"❌ Fatal error starting server: {e}")
		traceback.print_exc()
		sys.exit(1)