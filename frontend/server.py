import json
import sys
from flask import Flask, send_from_directory, jsonify, request
from flask_cors import CORS

sys.path.append('.')

from database.repositories import (
    load_mfd_json, get_all_races, get_race_by_id,
    get_races_by_filters
)

_PORT = 5000
_IP = '127.0.0.1'

# Set up Flask app
app = Flask(__name__, static_folder="./build")
CORS(app)

# Route to serve the main React app
@app.route('/')
def serve():
    # Serve the main index.html from the React build folder
    return send_from_directory(app.static_folder, 'index.html')

# API route to serve current telemetry data (for real-time mode)
@app.route('/api/mfd')
def mfd():
    # Load telemetry data from the backend (assuming you have this function)
    mfd_json = load_mfd_json()

    if mfd_json:
        return jsonify(json.loads(mfd_json))

    return jsonify({'error': 'No data available'}), 404

# API route to get all races with optional filters
@app.route('/api/races')
def get_races():
    try:
        # Get query parameters for filtering
        track_name = request.args.get('track_name')
        session_type = request.args.get('session_type')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        # Get races with filters
        if any([track_name, session_type, date_from, date_to]):
            races = get_races_by_filters(track_name, session_type, date_from, date_to)
        else:
            races = get_all_races()

        # Convert races to dictionaries for JSON serialization
        races_data = [race.to_dict() for race in races]

        return jsonify({
            'races': races_data,
            'total': len(races_data)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# API route to get a specific race by ID
@app.route('/api/races/<int:race_id>')
def get_race(race_id):
    try:
        race = get_race_by_id(race_id)

        if race:
            race_data = race.to_dict()
            # Include MFD data if available
            if race.mfd_data:
                race_data['mfd_data'] = json.loads(race.mfd_data)

            return jsonify(race_data)
        else:
            return jsonify({'error': 'Race not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# API route to get unique track names for filter dropdown
@app.route('/api/tracks')
def get_tracks():
    try:
        races = get_all_races()
        tracks = list(set(race.track_name for race in races if race.track_name))
        tracks.sort()

        return jsonify({'tracks': tracks})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# API route to get unique session types for filter dropdown
@app.route('/api/session-types')
def get_session_types():
    try:
        races = get_all_races()
        session_types = list(set(race.session_type for race in races if race.session_type))
        session_types.sort()

        return jsonify({'session_types': session_types})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Serve static files (JS, CSS, images, etc.) from the build folder
@app.route('/<path:path>')
def static_proxy(path):
    # Serve static files from the React build directory
    return send_from_directory(app.static_folder, path)

# Run the app
if __name__ == '__main__':
    app.run(debug=True, host=_IP, port=_PORT)
