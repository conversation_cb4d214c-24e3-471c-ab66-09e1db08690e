// /frontend/src/services/api.js
import axios from 'axios';

// Dynamically determine the API base URL based on the current location
const API_URL = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/api`;

export const fetchMFDData = async () => {
	try {
		const response = await axios.get(`${API_URL}/mfd`);
		return response.data;
	} catch (error) {
		console.error("Error fetching MFD data", error);
		return null;
	}
};

export const fetchRaces = async (filters = {}) => {
	try {
		const params = new URLSearchParams();
		if (filters.trackName) params.append('track_name', filters.trackName);
		if (filters.sessionType) params.append('session_type', filters.sessionType);
		if (filters.dateFrom) params.append('date_from', filters.dateFrom);
		if (filters.dateTo) params.append('date_to', filters.dateTo);

		const url = `${API_URL}/races${params.toString() ? '?' + params.toString() : ''}`;
		const response = await axios.get(url);
		return response.data;
	} catch (error) {
		console.error("Error fetching races", error);
		return { races: [], total: 0 };
	}
};

export const fetchRaceById = async (raceId) => {
	try {
		const response = await axios.get(`${API_URL}/races/${raceId}`);
		return response.data;
	} catch (error) {
		console.error("Error fetching race by ID", error);
		return null;
	}
};

export const fetchTracks = async () => {
	try {
		const response = await axios.get(`${API_URL}/tracks`);
		return response.data;
	} catch (error) {
		console.error("Error fetching tracks", error);
		return { tracks: [] };
	}
};

export const fetchSessionTypes = async () => {
	try {
		const response = await axios.get(`${API_URL}/session-types`);
		return response.data;
	} catch (error) {
		console.error("Error fetching session types", error);
		return { session_types: [] };
	}
};
