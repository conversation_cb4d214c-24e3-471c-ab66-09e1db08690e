#!/usr/bin/env python3
"""
Utilidades para gestión de la base de datos del F1 24 Dashboard
"""

import sys
import sqlite3
from datetime import datetime

sys.path.append('.')

from database.repositories import get_all_races, get_race_by_id

_DB_FILE = 'database/mfd.db'

def list_all_races():
    """Lista todas las carreras en la base de datos"""
    print("=" * 80)
    print("ALL RACES IN DATABASE")
    print("=" * 80)
    
    try:
        races = get_all_races()
        
        if not races:
            print("No races found in database.")
            return
        
        print(f"Total races: {len(races)}")
        print()
        
        for race in races:
            status = "✅ Completed" if race.is_completed else "🔄 In Progress"
            print(f"ID: {race.id}")
            print(f"  Session UID: {race.session_uid}")
            print(f"  Name: {race.session_name}")
            print(f"  Track: {race.track_name}")
            print(f"  Type: {race.session_type}")
            print(f"  Date: {race.timestamp}")
            print(f"  Status: {status}")
            print(f"  Data Size: {len(race.mfd_data) if race.mfd_data else 0} chars")
            print("-" * 40)
            
    except Exception as e:
        print(f"Error listing races: {e}")

def show_race_details(race_id):
    """Muestra detalles completos de una carrera específica"""
    try:
        race = get_race_by_id(race_id)
        
        if not race:
            print(f"Race with ID {race_id} not found.")
            return
        
        print("=" * 80)
        print(f"RACE DETAILS - ID: {race_id}")
        print("=" * 80)
        
        print(f"Session UID: {race.session_uid}")
        print(f"Track ID: {race.track_id}")
        print(f"Track Name: {race.track_name}")
        print(f"Session Type: {race.session_type}")
        print(f"Session Name: {race.session_name}")
        print(f"Timestamp: {race.timestamp}")
        print(f"Game Mode: {race.game_mode}")
        print(f"Total Laps: {race.total_laps}")
        print(f"Session Duration: {race.session_duration}")
        print(f"Weather: {race.weather}")
        print(f"Track Temperature: {race.track_temperature}")
        print(f"Air Temperature: {race.air_temperature}")
        print(f"Is Completed: {race.is_completed}")
        print(f"MFD Data Size: {len(race.mfd_data) if race.mfd_data else 0} characters")
        
        if race.mfd_data:
            print("\nMFD Data Preview (first 200 chars):")
            print(race.mfd_data[:200] + "..." if len(race.mfd_data) > 200 else race.mfd_data)
            
    except Exception as e:
        print(f"Error showing race details: {e}")

def clean_test_data():
    """Limpia datos de prueba (carreras con session_uid específicos)"""
    print("=" * 80)
    print("CLEAN TEST DATA")
    print("=" * 80)
    
    response = input("This will delete test races (session_uid 999999). Continue? (y/N): ")
    
    if response.lower() != 'y':
        print("Operation cancelled.")
        return
    
    try:
        conn = sqlite3.connect(_DB_FILE)
        c = conn.cursor()
        
        # Eliminar carreras de prueba
        c.execute('DELETE FROM races WHERE session_uid = 999999')
        deleted_count = c.rowcount
        
        conn.commit()
        conn.close()
        
        print(f"✅ Deleted {deleted_count} test race(s)")
        
    except Exception as e:
        print(f"❌ Error cleaning test data: {e}")

def backup_database():
    """Crea una copia de seguridad de la base de datos"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"database/mfd_backup_{timestamp}.db"
        
        # Copiar archivo de base de datos
        import shutil
        shutil.copy2(_DB_FILE, backup_file)
        
        print(f"✅ Database backed up to: {backup_file}")
        
    except Exception as e:
        print(f"❌ Error creating backup: {e}")

def show_database_stats():
    """Muestra estadísticas de la base de datos"""
    print("=" * 80)
    print("DATABASE STATISTICS")
    print("=" * 80)
    
    try:
        conn = sqlite3.connect(_DB_FILE)
        c = conn.cursor()
        
        # Contar carreras totales
        c.execute('SELECT COUNT(*) FROM races')
        total_races = c.fetchone()[0]
        
        # Contar carreras completadas
        c.execute('SELECT COUNT(*) FROM races WHERE is_completed = 1')
        completed_races = c.fetchone()[0]
        
        # Contar carreras por tipo de sesión
        c.execute('SELECT session_type, COUNT(*) FROM races GROUP BY session_type')
        session_types = c.fetchall()
        
        # Contar carreras por circuito
        c.execute('SELECT track_name, COUNT(*) FROM races GROUP BY track_name ORDER BY COUNT(*) DESC LIMIT 5')
        top_tracks = c.fetchall()
        
        # Fecha de la carrera más antigua y más reciente
        c.execute('SELECT MIN(timestamp), MAX(timestamp) FROM races')
        date_range = c.fetchone()
        
        conn.close()
        
        print(f"Total Races: {total_races}")
        print(f"Completed Races: {completed_races}")
        print(f"In Progress Races: {total_races - completed_races}")
        print()
        
        if session_types:
            print("Races by Session Type:")
            for session_type, count in session_types:
                print(f"  {session_type}: {count}")
            print()
        
        if top_tracks:
            print("Top 5 Tracks by Race Count:")
            for track, count in top_tracks:
                print(f"  {track}: {count}")
            print()
        
        if date_range[0] and date_range[1]:
            print(f"Date Range: {date_range[0]} to {date_range[1]}")
        
    except Exception as e:
        print(f"Error getting database stats: {e}")

def main():
    """Función principal del script de utilidades"""
    while True:
        print("\n" + "=" * 60)
        print("F1 24 Dashboard - Database Utilities")
        print("=" * 60)
        print("1. List all races")
        print("2. Show race details")
        print("3. Show database statistics")
        print("4. Clean test data")
        print("5. Backup database")
        print("6. Exit")
        print()
        
        choice = input("Select an option (1-6): ").strip()
        
        if choice == '1':
            list_all_races()
        elif choice == '2':
            race_id = input("Enter race ID: ").strip()
            try:
                race_id = int(race_id)
                show_race_details(race_id)
            except ValueError:
                print("Invalid race ID. Please enter a number.")
        elif choice == '3':
            show_database_stats()
        elif choice == '4':
            clean_test_data()
        elif choice == '5':
            backup_database()
        elif choice == '6':
            print("Goodbye!")
            break
        else:
            print("Invalid option. Please select 1-6.")

if __name__ == "__main__":
    main()
