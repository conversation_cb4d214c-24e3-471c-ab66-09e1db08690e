import socket, sys, json

sys.path.append('.')

from database.models import *
from database.repositories import (
    init_db, save_mfd, init_races_db, save_race,
    get_race_by_session_uid, mark_race_completed, create_race_from_mfd
)

from enums import PacketIDs, EventCodes

from packets import *

_PORT = 20777
_IP = '127.0.0.1'

sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
sock.bind((_IP, _PORT))

# MDF Panels
multi_function_display = MultiFunctionDisplay()

# Session tracking variables
current_session_uid = 0
current_race = None

def to_json(obj):
    return json.dumps(obj, default=lambda o: o.__dict__)

def handle_session_change(new_session_uid: int):
    global current_session_uid, current_race, multi_function_display

    if current_session_uid != 0 and current_session_uid != new_session_uid:
        # Guardar la sesión anterior como carrera completa
        if current_race:
            current_race.is_completed = True
            current_race.mfd_data = to_json(multi_function_display)
            save_race(current_race)
            print(f"Saved completed race: {current_race.session_name}")

        # Reiniciar el MFD para la nueva sesión
        multi_function_display = MultiFunctionDisplay()

    current_session_uid = new_session_uid

    # Crear nueva carrera para la nueva sesión
    current_race = create_race_from_mfd(
        multi_function_display,
        current_session_uid,
        multi_function_display.track_id,
        multi_function_display.track_name,
        multi_function_display.game_mode,
        multi_function_display.total_laps,
        multi_function_display.session_duration
    )
    save_race(current_race)
    print(f"Started new race: {current_race.session_name}")

def handle_session_started_event():
    """Maneja el evento SESSION_STARTED"""
    global current_race, multi_function_display

    if current_session_uid != 0:
        # Crear nueva carrera para la sesión actual
        current_race = create_race_from_mfd(
            multi_function_display,
            current_session_uid,
            multi_function_display.track_id,
            multi_function_display.track_name,
            multi_function_display.game_mode,
            multi_function_display.total_laps,
            multi_function_display.session_duration
        )
        save_race(current_race)
        print(f"Started new race: {current_race.session_name}")

def handle_session_ended_event():
    """Maneja el evento SESSION_ENDED"""
    global current_race, multi_function_display

    if current_race:
        current_race.is_completed = True
        current_race.mfd_data = to_json(multi_function_display)
        save_race(current_race)
        print(f"Completed race: {current_race.session_name}")

def update_current_race():
    """Actualiza la carrera actual con los datos del MFD"""
    global current_race, multi_function_display

    if current_race and current_session_uid != 0:
        current_race.mfd_data = to_json(multi_function_display)
        current_race.track_name = multi_function_display.track_name
        current_race.session_type = multi_function_display.session_type
        current_race.total_laps = multi_function_display.total_laps
        current_race.session_duration = multi_function_display.session_duration

        # Actualizar datos del clima si están disponibles
        weather_panel = multi_function_display.weather_panel()
        if hasattr(weather_panel, 'air_temperature'):
            current_race.air_temperature = weather_panel.air_temperature
        if hasattr(weather_panel, 'track_temperature'):
            current_race.track_temperature = weather_panel.track_temperature
        if hasattr(weather_panel, 'weather'):
            current_race.weather = weather_panel.weather

        # Actualizar nombre de sesión
        multi_function_display.update_session_name()
        current_race.session_name = multi_function_display.session_name

        save_race(current_race)

def start_server():
	print(f"Listening for UDP packets on port {_PORT}...")
	while True:
		data, _ = sock.recvfrom(2048)
		handle_packet(data)

def handle_packet(data: bytes):
	if len(data) >= PACKET_HEADER_FORMAT_SIZE:
			packet_header_bytes = data[:PACKET_HEADER_FORMAT_SIZE]
			packet_header = unpack_packet_header(packet_header_bytes)

			# Detectar cambio de sesión
			if packet_header.session_uid != current_session_uid:
				handle_session_change(packet_header.session_uid)

			remaning_bytes = data[PACKET_HEADER_FORMAT_SIZE:]

			match packet_header.packet_id:
				case PacketIDs.CAR_TELEMETRY.value:
					car_telemetry_packet = unpack_car_telemetry(packet_header, remaning_bytes)
					
					multi_function_display.update_from_car_telemetry_packet(car_telemetry_packet)

				case PacketIDs.CAR_STATUS.value:
					car_status_packet = unpack_car_status(packet_header, remaning_bytes)

					multi_function_display.update_from_car_status_packet(car_status_packet)

				case PacketIDs.CAR_DAMAGE.value:
					car_damage_packet = unpack_car_damage(packet_header, remaning_bytes)

					multi_function_display.update_from_car_damage_packet(car_damage_packet)

				case PacketIDs.TYRE_SETS.value:
					tyre_sets_packet = unpack_tyre_sets(packet_header, remaning_bytes)

					multi_function_display.update_from_tyre_sets_packet(tyre_sets_packet)
				
				case PacketIDs.LAP_DATA.value:
					lap_data_packet = unpack_lap_data(packet_header, remaning_bytes)

					multi_function_display.update_from_lap_data_packet(lap_data_packet)

				case PacketIDs.PARTICIPANTS.value:
					participants_packet = unpack_participants(packet_header, remaning_bytes)

					multi_function_display.update_from_participants_packet(participants_packet)

				case PacketIDs.SESSION.value:
					session_packet = unpack_session(packet_header, remaning_bytes)

					multi_function_display.update_from_session_packet(session_packet)

				case PacketIDs.EVENT.value:
					event_packet = unpack_event_packet(packet_header, remaning_bytes)
					event = event_packet.event

					# Convertir event_string_code a string para comparar
					event_code = ''.join(chr(b) for b in event_packet.event_string_code)

					# Session Events
					if event_code == EventCodes.SESSION_STARTED.value:
						handle_session_started_event()
					elif event_code == EventCodes.SESSION_ENDED.value:
						handle_session_ended_event()
					elif event_code == EventCodes.CHEQUERED_FLAG.value:
						handle_session_ended_event()

					# Button Events
					elif type(event) == ButtonsEvent:
						if (event.button_status & ButtonFlags.UDP_ACTION_1):
							multi_function_display.navigate_left()

						elif (event.button_status & ButtonFlags.UDP_ACTION_2):
							multi_function_display.navigate_right()

						elif (event.button_status & ButtonFlags.UDP_ACTION_3):
							multi_function_display.focus_mode_toggle()

					elif type(event) == SafetyCarEvent:
						multi_function_display.update_from_safety_car_event(event)

					elif type(event) == FastestLapEvent:
						multi_function_display.update_from_fastest_lap_event(event)

			# Guardar MFD (para compatibilidad con sistema existente)
			save_mfd(multi_function_display)

			# Actualizar carrera actual si existe
			update_current_race()

if __name__ == '__main__':
	init_db()
	init_races_db()
	print("Database initialized for multiple races support")
	start_server()